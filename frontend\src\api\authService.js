import axiosClient from './axiosClient';

const authService = {
  // Register a new user
  register: async (userData) => {
    try {
      const response = await axiosClient.post('/auth/register', userData);

      // Store user data and token in localStorage if provided
      if (response.token) {
        localStorage.setItem('token', response.token);
        localStorage.setItem('user', JSON.stringify(response.user));
      }

      return response;
    } catch (error) {
      throw error;
    }
  },

  // Login user
  login: async (credentials) => {
    try {
      const response = await axiosClient.post('/auth/login', credentials);

      // Store user data and token in localStorage
      if (response.token) {
        localStorage.setItem('token', response.token);
        localStorage.setItem('user', JSON.stringify(response.user));
      }

      return response;
    } catch (error) {
      throw error;
    }
  },

  // Get current user
  getCurrentUser: async () => {
    try {
      const response = await axiosClient.get('/auth/me');
      if (response.user) {
        localStorage.setItem('user', JSON.stringify(response.user));
        localStorage.setItem('token', response.token);
      } else {
        localStorage.removeItem('user');
        localStorage.removeItem('token');
      }
      return response;
    } catch (error) {
      throw error;
    }
  },


  // Verify user email
  verifyEmail: async (verificationData) => {
    try {
      const response = await axiosClient.post('/auth/verify-email', verificationData);
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Request password reset
  forgotPassword: async (email) => {
    try {
      const response = await axiosClient.post('/auth/forgot-password', { email });
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Reset password
  resetPassword: async (resetData) => {
    try {
      const response = await axiosClient.post('/auth/reset-password', resetData);
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Logout user
  logout: () => {
    // Remove user data and token from localStorage
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  },

  // Check if user is authenticated
  isAuthenticated: () => {
    const token = localStorage.getItem('token');
    return !!token;
  },

  // Get user from localStorage
  getUser: () => {
    const user = localStorage.getItem('user');
    return user ? JSON.parse(user) : null;
  },
};

export default authService;
