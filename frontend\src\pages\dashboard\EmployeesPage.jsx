import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Users,
  UserPlus,
  Download,
  Search,
  Edit,
  Trash2,
  Eye,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import FilterDropdown from "../../components/common/FilterDropdown";
import Pagination from "../../components/common/Pagination";
import AddEmployeeModal from "../../components/employees/AddEmployeeModal";
import EditEmployeeModal from "../../components/employees/EditEmployeeModal";

// Mock employee data
const mockEmployees = [
  {
    id: 1,
    name: "<PERSON>",
    position: "Senior Developer",
    role: "Admin",
    dateJoined: "2022-01-15",
    department: "Engineering",
  },
  {
    id: 2,
    name: "<PERSON>",
    position: "UI/UX Designer",
    role: "User",
    dateJoined: "2022-03-10",
    department: "Design",
  },
  {
    id: 3,
    name: "<PERSON>",
    position: "Project Manager",
    role: "Manager",
    dateJoined: "2021-11-05",
    department: "Product",
  },
  {
    id: 4,
    name: "<PERSON>",
    position: "HR Specialist",
    role: "User",
    dateJoined: "2022-05-20",
    department: "HR",
  },
  {
    id: 5,
    name: "<PERSON> <PERSON>",
    position: "Backend Developer",
    role: "User",
    dateJoined: "2022-02-28",
    department: "Engineering",
  },
  {
    id: 6,
    name: "Sarah Brown",
    position: "Financial Analyst",
    role: "User",
    dateJoined: "2022-04-12",
    department: "Finance",
  },
  {
    id: 7,
    name: "David Miller",
    position: "Product Owner",
    role: "Manager",
    dateJoined: "2021-10-18",
    department: "Product",
  },
  {
    id: 8,
    name: "Lisa Taylor",
    position: "Frontend Developer",
    role: "User",
    dateJoined: "2022-06-05",
    department: "Engineering",
  },
];

const EmployeesPage = () => {
  const navigate = useNavigate();
  const [employees, setEmployees] = useState(mockEmployees);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortField, setSortField] = useState("name");
  const [sortDirection, setSortDirection] = useState("asc");
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [currentEmployee, setCurrentEmployee] = useState(null);
  const [filterOption, setFilterOption] = useState("Today");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [dateRange, setDateRange] = useState({
    startDate: null,
    endDate: null,
  });

  const filterOptions = [
    "Today",
    "Yesterday",
    "This Week",
    "This Month",
    "Last Month",
    "All Time",
    "Department: Engineering",
    "Department: Design",
    "Department: Product",
    "Department: HR",
    "Department: Finance",
    "Role: Admin",
    "Role: Manager",
    "Role: User",
  ];

  // Reset to first page when search term or filter changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, filterOption, dateRange]);

  // Handle sorting
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  // Sort employees
  const sortedEmployees = [...employees].sort((a, b) => {
    if (a[sortField] < b[sortField]) {
      return sortDirection === "asc" ? -1 : 1;
    }
    if (a[sortField] > b[sortField]) {
      return sortDirection === "asc" ? 1 : -1;
    }
    return 0;
  });

  // Filter employees based on search term
  const filteredEmployees = sortedEmployees.filter(
    (employee) =>
      employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.position.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.role.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Get current page items
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredEmployees.slice(
    indexOfFirstItem,
    indexOfLastItem
  );

  // Change page
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  // Change items per page
  const handleItemsPerPageChange = (newItemsPerPage) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Handle delete employee
  const handleDelete = (id) => {
    if (window.confirm("Are you sure you want to delete this employee?")) {
      setEmployees(employees.filter((employee) => employee.id !== id));
    }
  };

  // Handle edit employee
  const handleEdit = (employee) => {
    setCurrentEmployee(employee);
    setShowEditModal(true);
  };

  // Handle save edited employee
  const handleSaveEdit = (updatedEmployee) => {
    setEmployees(
      employees.map((emp) =>
        emp.id === updatedEmployee.id ? updatedEmployee : emp
      )
    );
  };

  // Handle add employee
  const handleAddEmployee = (newEmployee) => {
    // For single employee
    if (!Array.isArray(newEmployee)) {
      const newId = Math.max(...employees.map((emp) => emp.id)) + 1;
      setEmployees([...employees, { ...newEmployee, id: newId }]);
    }
    // For CSV import (array of employees)
    else {
      const lastId = Math.max(...employees.map((emp) => emp.id));
      const newEmployees = newEmployee.map((emp, index) => ({
        ...emp,
        id: lastId + index + 1,
      }));
      setEmployees([...employees, ...newEmployees]);
    }
  };

  // Handle filter change
  const handleFilterChange = (option) => {
    setFilterOption(option);
    setDateRange({ startDate: null, endDate: null });

    // Apply filtering logic based on the selected option
    // This is a simplified example - you would implement more complex filtering
    if (option.startsWith("Department:")) {
      const department = option.split(": ")[1];
      setSearchTerm(department);
    } else if (option.startsWith("Role:")) {
      const role = option.split(": ")[1];
      setSearchTerm(role);
    } else {
      // Time-based filtering would be implemented here
      // For now, just clear the search term for time filters
      setSearchTerm("");
    }
  };

  // Handle date range change
  const handleDateRangeChange = (startDate, endDate) => {
    setDateRange({ startDate, endDate });
    setFilterOption("Custom Range");

    // In a real app, you would filter based on the date range
    // For now, we'll just log it
    console.log("Date range:", startDate, endDate);
  };

  // Export employees as CSV
  const exportEmployees = () => {
    const headers = ["Name", "Position", "Role", "Date Joined", "Department"];
    const csvData = [
      headers.join(","),
      ...employees.map((employee) =>
        [
          employee.name,
          employee.position,
          employee.role,
          employee.dateJoined,
          employee.department,
        ].join(",")
      ),
    ].join("\n");

    const blob = new Blob([csvData], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "employees.csv";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Employees</h2>
          <p className="mt-1 text-sm text-text-secondary">
            Manage your team members
          </p>
        </div>
        <div className="flex mt-4 space-x-3 md:mt-0">
          <button
            onClick={exportEmployees}
            className="flex items-center px-4 py-2 text-sm font-medium rounded-lg btn-white-ghost"
          >
            <Download className="mr-2 w-4 h-4" />
            Export
          </button>
          <button
            onClick={() => setShowAddModal(true)}
            className="flex items-center px-4 py-2 text-sm font-medium rounded-lg btn-white"
          >
            <UserPlus className="mr-2 w-4 h-4" />
            Add Employee
          </button>
        </div>
      </div>

      {/* Search and filter */}
      <div className="p-4 rounded-lg glass-card">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div className="relative flex-1">
            <div className="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
              <Search className="w-4 h-4 text-text-secondary" />
            </div>
            <input
              type="text"
              placeholder="Search employees..."
              className="py-2 pr-4 pl-10 w-full rounded-lg input-dark"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex items-center">
            <FilterDropdown
              options={filterOptions}
              defaultOption="Today"
              onFilterChange={handleFilterChange}
              onDateRangeChange={handleDateRangeChange}
              showDateRangePicker={true}
            />
          </div>
        </div>
      </div>

      {/* Employees table */}
      <div className="overflow-hidden rounded-lg glass-card">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="border-b bg-surface-2 border-border">
              <tr>
                <th
                  className="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase cursor-pointer text-text-secondary"
                  onClick={() => handleSort("name")}
                >
                  <div className="flex items-center">
                    Name
                    {sortField === "name" &&
                      (sortDirection === "asc" ? (
                        <ChevronUp className="ml-1 w-4 h-4" />
                      ) : (
                        <ChevronDown className="ml-1 w-4 h-4" />
                      ))}
                  </div>
                </th>
                <th
                  className="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase cursor-pointer text-text-secondary"
                  onClick={() => handleSort("position")}
                >
                  <div className="flex items-center">
                    Position
                    {sortField === "position" &&
                      (sortDirection === "asc" ? (
                        <ChevronUp className="ml-1 w-4 h-4" />
                      ) : (
                        <ChevronDown className="ml-1 w-4 h-4" />
                      ))}
                  </div>
                </th>
                <th
                  className="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase cursor-pointer text-text-secondary"
                  onClick={() => handleSort("role")}
                >
                  <div className="flex items-center">
                    Role
                    {sortField === "role" &&
                      (sortDirection === "asc" ? (
                        <ChevronUp className="ml-1 w-4 h-4" />
                      ) : (
                        <ChevronDown className="ml-1 w-4 h-4" />
                      ))}
                  </div>
                </th>
                <th
                  className="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase cursor-pointer text-text-secondary"
                  onClick={() => handleSort("dateJoined")}
                >
                  <div className="flex items-center">
                    Date Joined
                    {sortField === "dateJoined" &&
                      (sortDirection === "asc" ? (
                        <ChevronUp className="ml-1 w-4 h-4" />
                      ) : (
                        <ChevronDown className="ml-1 w-4 h-4" />
                      ))}
                  </div>
                </th>
                <th
                  className="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase cursor-pointer text-text-secondary"
                  onClick={() => handleSort("department")}
                >
                  <div className="flex items-center">
                    Department
                    {sortField === "department" &&
                      (sortDirection === "asc" ? (
                        <ChevronUp className="ml-1 w-4 h-4" />
                      ) : (
                        <ChevronDown className="ml-1 w-4 h-4" />
                      ))}
                  </div>
                </th>
                <th className="px-6 py-3 text-xs font-medium tracking-wider text-right uppercase text-text-secondary">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-border">
              {currentItems.length > 0 ? (
                currentItems.map((employee) => (
                  <tr key={employee.id} className="hover:bg-surface-2">
                    <td className="px-6 py-4 text-sm text-white whitespace-nowrap">
                      {employee.name}
                    </td>
                    <td className="px-6 py-4 text-sm text-white whitespace-nowrap">
                      {employee.position}
                    </td>
                    <td className="px-6 py-4 text-sm whitespace-nowrap">
                      <span
                        className={`px-2 py-1 text-xs rounded-full ${
                          employee.role === "Admin"
                            ? "bg-purple-900/30 text-purple-300"
                            : employee.role === "Manager"
                            ? "bg-blue-900/30 text-blue-300"
                            : "bg-green-900/30 text-green-300"
                        }`}
                      >
                        {employee.role}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-white whitespace-nowrap">
                      {formatDate(employee.dateJoined)}
                    </td>
                    <td className="px-6 py-4 text-sm text-white whitespace-nowrap">
                      {employee.department}
                    </td>
                    <td className="px-6 py-4 text-sm text-right whitespace-nowrap">
                      <div className="flex justify-end space-x-2">
                        <button
                          className="p-1 text-text-secondary hover:text-white"
                          onClick={() =>
                            navigate(`/dashboard/employees/${employee.id}`)
                          }
                        >
                          <Eye size={16} />
                        </button>
                        <button
                          className="p-1 text-text-secondary hover:text-white"
                          onClick={() => handleEdit(employee)}
                        >
                          <Edit size={16} />
                        </button>
                        <button
                          className="p-1 text-text-secondary hover:text-accent-4"
                          onClick={() => handleDelete(employee.id)}
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan="6"
                    className="px-6 py-4 text-sm text-center text-text-secondary"
                  >
                    No employees found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="mt-6">
        <Pagination
          currentPage={currentPage}
          totalPages={Math.ceil(filteredEmployees.length / itemsPerPage)}
          onPageChange={handlePageChange}
          totalItems={filteredEmployees.length}
          itemsPerPage={itemsPerPage}
          onItemsPerPageChange={handleItemsPerPageChange}
          showItemsPerPageSelect={true}
          itemsPerPageOptions={[5, 10, 25, 50]}
          className="p-4 rounded-lg glass-card"
        />
      </div>

      {/* Add Employee Modal */}
      <AddEmployeeModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onAddEmployee={handleAddEmployee}
      />

      {/* Edit Employee Modal */}
      <EditEmployeeModal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        employee={currentEmployee}
        onSave={handleSaveEdit}
      />
    </div>
  );
};

export default EmployeesPage;
