import nodemailer from 'nodemailer';
import dotenv from 'dotenv';

dotenv.config();

// Create transporter based on environment
const createTransporter = () => {
  if (process.env.NODE_ENV === 'production') {
    // Production email configuration (e.g., SendGrid, AWS SES, etc.)
    return nodemailer.createTransporter({
      service: process.env.EMAIL_SERVICE || 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASSWORD
      }
    });
  } else {
    // Development - use Ethereal Email for testing
    return nodemailer.createTransporter({
      host: 'smtp.ethereal.email',
      port: 587,
      auth: {
        user: process.env.ETHEREAL_USER || '<EMAIL>',
        pass: process.env.ETHEREAL_PASS || 'ethereal.pass'
      }
    });
  }
};

// Send waitlist confirmation email
const sendWaitlistConfirmation = async (email) => {
  try {
    const transporter = createTransporter();
    
    const mailOptions = {
      from: process.env.EMAIL_FROM || '<EMAIL>',
      to: email,
      subject: 'Welcome to TeamSzly Waitlist! 🎉',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #1a1a1a; margin-bottom: 10px;">Welcome to TeamSzly!</h1>
            <p style="color: #666; font-size: 16px;">You're now on our exclusive waitlist</p>
          </div>
          
          <div style="background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%); padding: 30px; border-radius: 10px; margin-bottom: 30px;">
            <h2 style="color: #1a1a1a; margin-bottom: 15px;">What's Next?</h2>
            <ul style="color: #333; line-height: 1.6;">
              <li>🎯 You'll be among the first to access TeamSzly when we launch</li>
              <li>💰 Get 50% off your first 3 months as a waitlist member</li>
              <li>🆓 Enjoy a 14-day free trial with full access</li>
              <li>📧 Receive exclusive updates and early access invitations</li>
            </ul>
          </div>
          
          <div style="text-align: center; margin-bottom: 30px;">
            <h3 style="color: #1a1a1a;">Why TeamSzly?</h3>
            <p style="color: #666; line-height: 1.6;">
              TeamSzly simplifies team management with 360° oversight, automated payroll, 
              and reduced meeting time. Everything you need to manage your team effectively 
              in one powerful dashboard.
            </p>
          </div>
          
          <div style="text-align: center; padding: 20px; background: #f9f9f9; border-radius: 8px;">
            <p style="color: #666; margin: 0;">
              Questions? Reply to this email or visit our website.<br>
              Thank you for joining us on this journey!
            </p>
            <p style="color: #999; font-size: 14px; margin-top: 15px;">
              © ${new Date().getFullYear()} TeamSzly. All rights reserved.
            </p>
          </div>
        </div>
      `
    };

    const result = await transporter.sendMail(mailOptions);
    console.log('Waitlist confirmation email sent:', result.messageId);
    return { success: true, messageId: result.messageId };
  } catch (error) {
    console.error('Error sending waitlist confirmation email:', error);
    return { success: false, error: error.message };
  }
};

export {
  sendWaitlistConfirmation
};
