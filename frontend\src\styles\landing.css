/* Landing page specific styles */

/* Animated background */
@keyframes slow-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes slow-spin-reverse {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(-360deg);
  }
}

.animate-slow-spin {
  animation: slow-spin 60s linear infinite;
}

.animate-slow-spin-reverse {
  animation: slow-spin-reverse 50s linear infinite;
}

/* Glowing text effect */
.glow-text {
  text-shadow: 0 0 10px rgba(79, 70, 229, 0.7),
               0 0 20px rgba(79, 70, 229, 0.5),
               0 0 30px rgba(79, 70, 229, 0.3);
}

/* Glass card effect */
.glass-card {
  background: rgba(26, 26, 26, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

/* Gradient borders */
.gradient-border {
  position: relative;
  border-radius: 0.5rem;
  overflow: hidden;
}

.gradient-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #4f46e5, #0ea5e9, #10b981, #4f46e5);
  z-index: -1;
  border-radius: 0.6rem;
  animation: border-animation 6s linear infinite;
}

@keyframes border-animation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Floating animation for cards */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delay-1 {
  animation: float 6s ease-in-out 1s infinite;
}

.animate-float-delay-2 {
  animation: float 6s ease-in-out 2s infinite;
}

/* Pulse animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.animate-pulse-slow {
  animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Gradient text */
.gradient-text {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(to right, #4f46e5, #0ea5e9, #10b981);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .glass-card {
    backdrop-filter: blur(5px);
  }
}
