# Redux Toolkit State Management

This directory contains the Redux Toolkit setup for the TeamCheck frontend application. Redux Toolkit is used for global state management across the application.

## Structure

```
src/store/
├── index.js              # Store configuration
├── hooks.js              # Typed hooks for useDispatch and useSelector
├── slices/
│   ├── authSlice.js       # Authentication state
│   ├── companiesSlice.js  # Companies management
│   ├── usersSlice.js      # Users management
│   ├── projectsSlice.js   # Projects management
│   ├── tasksSlice.js      # Tasks management
│   ├── attendanceSlice.js # Attendance tracking
│   ├── leavesSlice.js     # Leave management
│   └── evaluationsSlice.js # Performance evaluations
└── README.md             # This file
```

## Usage

### 1. Using Redux Hooks

```javascript
import { useAppDispatch, useAppSelector } from '../store/hooks';

const MyComponent = () => {
  const dispatch = useAppDispatch();
  const { user, loading, error } = useAppSelector((state) => state.auth);

  // Component logic here
};
```

### 2. Dispatching Actions

```javascript
import { loginUser, clearError } from '../store/slices/authSlice';

const handleLogin = async () => {
  // Clear any previous errors
  dispatch(clearError());

  try {
    // Dispatch async action
    const result = await dispatch(loginUser({
      email: '<EMAIL>',
      password: 'password123'
    }));

    // Check if action was successful
    if (loginUser.fulfilled.match(result)) {
      console.log('Login successful');
      navigate('/dashboard');
    }
  } catch (error) {
    console.error('Login failed:', error);
  }
};
```

### 3. Reading State

```javascript
// Auth state
const { user, isAuthenticated, loading, error } = useAppSelector((state) => state.auth);

// Companies state
const { companies, currentCompany, loading: companiesLoading } = useAppSelector((state) => state.companies);

// Multiple slices
const {
  auth: { user, isAuthenticated },
  companies: { companies, currentCompany },
  projects: { projects, currentProject }
} = useAppSelector((state) => state);
```

## Available Slices

### Auth Slice (`authSlice.js`)

**Actions:**
- `loginUser(credentials)` - Login user
- `registerUser(userData)` - Register new user
- `getCurrentUser()` - Get current user data
- `forgotPassword(email)` - Request password reset
- `resetPassword(resetData)` - Reset password
- `verifyEmail(verificationData)` - Verify email
- `logout()` - Logout user
- `clearError()` - Clear error state

**State:**
```javascript
{
  user: null | UserObject,
  token: string | null,
  isAuthenticated: boolean,
  loading: boolean,
  error: string | null,
  passwordResetSent: boolean,
  emailVerified: boolean
}
```

### Companies Slice (`companiesSlice.js`)

**Actions:**
- `fetchCompanies()` - Get all companies
- `createCompany(companyData)` - Create new company
- `updateCompany({ id, companyData })` - Update company
- `deleteCompany(id)` - Delete company
- `fetchCompanyUsers(companyId)` - Get company users
- `inviteUserToCompany({ companyId, inviteData })` - Invite user

**State:**
```javascript
{
  companies: [],
  currentCompany: null | CompanyObject,
  companyUsers: [],
  loading: boolean,
  error: string | null,
  createLoading: boolean,
  updateLoading: boolean,
  deleteLoading: boolean,
  inviteLoading: boolean
}
```

### Projects Slice (`projectsSlice.js`)

**Actions:**
- `fetchProjects()` - Get all projects
- `createProject(projectData)` - Create new project
- `fetchProjectById(projectId)` - Get project by ID
- `updateProject({ id, projectData })` - Update project
- `deleteProject(projectId)` - Delete project
- `addProjectMember({ projectId, memberData })` - Add member
- `removeProjectMember({ projectId, userId })` - Remove member

### Attendance Slice (`attendanceSlice.js`)

**Actions:**
- `fetchAttendanceRecords(params)` - Get attendance records
- `checkIn(checkInData)` - Record check-in
- `checkOut(checkOutData)` - Record check-out
- `fetchDailyOverview(params)` - Get daily overview
- `fetchDetailedLogs(params)` - Get detailed logs
- `fetchHoursBreakdown(params)` - Get hours breakdown

## Best Practices

### 1. Error Handling

```javascript
const handleAction = async () => {
  try {
    const result = await dispatch(someAsyncAction(data));

    if (someAsyncAction.fulfilled.match(result)) {
      // Success handling
      console.log('Action successful');
    } else if (someAsyncAction.rejected.match(result)) {
      // Error handling
      console.error('Action failed:', result.payload);
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
};
```

### 2. Loading States

```javascript
const MyComponent = () => {
  const { loading, createLoading, updateLoading } = useAppSelector((state) => state.companies);

  return (
    <div>
      {loading && <LoadingSpinner />}
      <button disabled={createLoading}>
        {createLoading ? 'Creating...' : 'Create'}
      </button>
    </div>
  );
};
```

### 3. Clearing Errors

```javascript
useEffect(() => {
  // Clear errors when component mounts
  dispatch(clearError());

  return () => {
    // Optionally clear errors when component unmounts
    dispatch(clearError());
  };
}, [dispatch]);
```

### 4. Logout Functionality

```javascript
import { logout } from '../store/slices/authSlice';
import { useNavigate } from 'react-router-dom';

const LogoutButton = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const handleLogout = () => {
    // Optional: Show confirmation
    const confirmLogout = window.confirm('Are you sure you want to logout?');

    if (confirmLogout) {
      // Dispatch logout action
      dispatch(logout());

      // Navigate to login page
      navigate('/login');
    }
  };

  return (
    <button onClick={handleLogout}>
      Logout
    </button>
  );
};
```

### 5. Conditional Rendering

```javascript
const MyComponent = () => {
  const { isAuthenticated, user } = useAppSelector((state) => state.auth);

  if (!isAuthenticated) {
    return <LoginPrompt />;
  }

  return (
    <div>
      <h1>Welcome, {user.name}!</h1>
      {/* Authenticated content */}
    </div>
  );
};
```

## Integration with API Services

The Redux slices are integrated with the API services. Each async thunk automatically:

1. Sets loading state to `true` when action starts
2. Calls the appropriate API service
3. Updates state with response data on success
4. Sets error state on failure
5. Sets loading state to `false` when action completes

## Examples

See `src/examples/ReduxUsageExample.jsx` for comprehensive examples of how to use each slice in a real component.

## JavaScript Implementation

This project uses JavaScript with Redux Toolkit. The setup provides excellent developer experience with:

- **Redux DevTools**: Full debugging capabilities
- **Immutable Updates**: Automatic with Redux Toolkit
- **Async Actions**: Built-in with createAsyncThunk
- **Error Handling**: Centralized error management
- **Loading States**: Automatic loading state management

The hooks in `hooks.js` provide a consistent API for accessing Redux state and dispatch throughout the application.
