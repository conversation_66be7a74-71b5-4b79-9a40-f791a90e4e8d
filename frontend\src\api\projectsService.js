import axiosClient from './axiosClient';

const projectsService = {
  // Get all projects
  getProjects: async () => {
    try {
      const response = await axiosClient.get('/projects');
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Create new project
  createProject: async (projectData) => {
    try {
      const response = await axiosClient.post('/projects', projectData);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Get project by ID
  getProjectById: async (id) => {
    try {
      const response = await axiosClient.get(`/projects/${id}`);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Update project
  updateProject: async (id, projectData) => {
    try {
      const response = await axiosClient.put(`/projects/${id}`, projectData);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Delete project
  deleteProject: async (id) => {
    try {
      const response = await axiosClient.delete(`/projects/${id}`);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Add members to project
  addProjectMember: async (id, memberData) => {
    try {
      const response = await axiosClient.post(`/projects/${id}/members`, memberData);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Remove member from project
  removeProjectMember: async (id, userId) => {
    try {
      const response = await axiosClient.delete(`/projects/${id}/members/${userId}`);
      return response;
    } catch (error) {
      throw error;
    }
  },
};

export default projectsService;
