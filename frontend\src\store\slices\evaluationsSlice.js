import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { evaluationsService } from '../../api';

// Async thunks for evaluations operations
export const fetchEvaluations = createAsyncThunk(
  'evaluations/fetchEvaluations',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await evaluationsService.getEvaluations(params);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to fetch evaluations'
      );
    }
  }
);

export const createEvaluation = createAsyncThunk(
  'evaluations/createEvaluation',
  async (evaluationData, { rejectWithValue }) => {
    try {
      const response = await evaluationsService.createEvaluation(evaluationData);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to create evaluation'
      );
    }
  }
);

export const fetchEvaluationById = createAsyncThunk(
  'evaluations/fetchEvaluationById',
  async (evaluationId, { rejectWithValue }) => {
    try {
      const response = await evaluationsService.getEvaluationById(evaluationId);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to fetch evaluation'
      );
    }
  }
);

export const updateEvaluation = createAsyncThunk(
  'evaluations/updateEvaluation',
  async ({ id, evaluationData }, { rejectWithValue }) => {
    try {
      const response = await evaluationsService.updateEvaluation(id, evaluationData);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to update evaluation'
      );
    }
  }
);

export const deleteEvaluation = createAsyncThunk(
  'evaluations/deleteEvaluation',
  async (evaluationId, { rejectWithValue }) => {
    try {
      await evaluationsService.deleteEvaluation(evaluationId);
      return evaluationId;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to delete evaluation'
      );
    }
  }
);

export const completeEvaluation = createAsyncThunk(
  'evaluations/completeEvaluation',
  async ({ id, completionData }, { rejectWithValue }) => {
    try {
      const response = await evaluationsService.completeEvaluation(id, completionData);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to complete evaluation'
      );
    }
  }
);

// Initial state
const initialState = {
  evaluations: [],
  currentEvaluation: null,
  loading: false,
  error: null,
  createLoading: false,
  updateLoading: false,
  deleteLoading: false,
  completeLoading: false,
};

// Evaluations slice
const evaluationsSlice = createSlice({
  name: 'evaluations',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentEvaluation: (state, action) => {
      state.currentEvaluation = action.payload;
    },
    clearCurrentEvaluation: (state) => {
      state.currentEvaluation = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch evaluations
    builder
      .addCase(fetchEvaluations.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchEvaluations.fulfilled, (state, action) => {
        state.loading = false;
        state.evaluations = action.payload.evaluations || action.payload;
        state.error = null;
      })
      .addCase(fetchEvaluations.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
    // Create evaluation
    builder
      .addCase(createEvaluation.pending, (state) => {
        state.createLoading = true;
        state.error = null;
      })
      .addCase(createEvaluation.fulfilled, (state, action) => {
        state.createLoading = false;
        state.evaluations.push(action.payload.evaluation || action.payload);
        state.error = null;
      })
      .addCase(createEvaluation.rejected, (state, action) => {
        state.createLoading = false;
        state.error = action.payload;
      })
      
    // Fetch evaluation by ID
    builder
      .addCase(fetchEvaluationById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchEvaluationById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentEvaluation = action.payload.evaluation || action.payload;
        state.error = null;
      })
      .addCase(fetchEvaluationById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
    // Update evaluation
    builder
      .addCase(updateEvaluation.pending, (state) => {
        state.updateLoading = true;
        state.error = null;
      })
      .addCase(updateEvaluation.fulfilled, (state, action) => {
        state.updateLoading = false;
        const updatedEvaluation = action.payload.evaluation || action.payload;
        const index = state.evaluations.findIndex(evaluation => evaluation.id === updatedEvaluation.id);
        if (index !== -1) {
          state.evaluations[index] = updatedEvaluation;
        }
        if (state.currentEvaluation && state.currentEvaluation.id === updatedEvaluation.id) {
          state.currentEvaluation = updatedEvaluation;
        }
        state.error = null;
      })
      .addCase(updateEvaluation.rejected, (state, action) => {
        state.updateLoading = false;
        state.error = action.payload;
      })
      
    // Delete evaluation
    builder
      .addCase(deleteEvaluation.pending, (state) => {
        state.deleteLoading = true;
        state.error = null;
      })
      .addCase(deleteEvaluation.fulfilled, (state, action) => {
        state.deleteLoading = false;
        state.evaluations = state.evaluations.filter(evaluation => evaluation.id !== action.payload);
        if (state.currentEvaluation && state.currentEvaluation.id === action.payload) {
          state.currentEvaluation = null;
        }
        state.error = null;
      })
      .addCase(deleteEvaluation.rejected, (state, action) => {
        state.deleteLoading = false;
        state.error = action.payload;
      })
      
    // Complete evaluation
    builder
      .addCase(completeEvaluation.pending, (state) => {
        state.completeLoading = true;
        state.error = null;
      })
      .addCase(completeEvaluation.fulfilled, (state, action) => {
        state.completeLoading = false;
        const completedEvaluation = action.payload.evaluation || action.payload;
        const index = state.evaluations.findIndex(evaluation => evaluation.id === completedEvaluation.id);
        if (index !== -1) {
          state.evaluations[index] = completedEvaluation;
        }
        if (state.currentEvaluation && state.currentEvaluation.id === completedEvaluation.id) {
          state.currentEvaluation = completedEvaluation;
        }
        state.error = null;
      })
      .addCase(completeEvaluation.rejected, (state, action) => {
        state.completeLoading = false;
        state.error = action.payload;
      });
  },
});

export const { clearError, setCurrentEvaluation, clearCurrentEvaluation } = evaluationsSlice.actions;
export default evaluationsSlice.reducer;
