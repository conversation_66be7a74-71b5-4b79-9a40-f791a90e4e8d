import { useState, useEffect } from "react";
import { Calendar, Users, Briefcase } from "lucide-react";
import Modal from "../common/Modal";

const CreateEvaluationModal = ({
  isOpen,
  onClose,
  onCreateEvaluation,
  evaluationType = "periodic",
  projectId = null,
}) => {
  // Mock data for employees
  const mockEmployees = [
    {
      id: 1,
      name: "<PERSON>",
      position: "Frontend Developer",
      dateJoined: "2023-01-15",
    },
    {
      id: 2,
      name: "<PERSON>",
      position: "UX Designer",
      dateJoined: "2022-08-10",
    },
    {
      id: 3,
      name: "<PERSON>",
      position: "Backend Developer",
      dateJoined: "2023-03-22",
    },
    {
      id: 4,
      name: "<PERSON>",
      position: "Project Manager",
      dateJoined: "2021-11-05",
    },
  ];

  // Mock data for projects
  const mockProjects = [
    { id: 1, title: "Website Redesign" },
    { id: 2, title: "Mobile App Development" },
    { id: 3, title: "Client Onboarding Improvement" },
    { id: 4, title: "Database Migration" },
  ];

  // State for form data
  const [formData, setFormData] = useState({
    type: evaluationType,
    employeeId: "",
    projectId: projectId || "",
    date: new Date().toISOString().split("T")[0],
  });

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setFormData({
        type: evaluationType,
        employeeId: "",
        projectId: projectId || "",
        date: new Date().toISOString().split("T")[0],
      });
    }
  }, [isOpen, evaluationType, projectId]);

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    // Create evaluation object
    const evaluation = {
      id: Date.now(),
      type: formData.type,
      employeeId: formData.employeeId,
      projectId: formData.projectId,
      date: formData.date,
      status: "In Progress",
      createdAt: new Date().toISOString(),
      completedAt: null,
    };

    onCreateEvaluation(evaluation);
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        formData.type === "periodic"
          ? "Create Periodic Evaluation"
          : "Create Project Review"
      }
      size="md"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Employee Selection */}
        <div>
          <label
            htmlFor="employeeId"
            className="block text-sm font-medium text-white mb-1"
          >
            Employee
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Users className="h-5 w-5 text-text-secondary" />
            </div>
            <select
              id="employeeId"
              name="employeeId"
              value={formData.employeeId}
              onChange={handleInputChange}
              className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full pl-10 p-2.5"
              required
            >
              <option value="">Select Employee</option>
              {mockEmployees.map((employee) => (
                <option key={employee.id} value={employee.id}>
                  {employee.name} - {employee.position}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Project Selection (only for project reviews) */}
        {formData.type === "project" && (
          <div>
            <label
              htmlFor="projectId"
              className="block text-sm font-medium text-white mb-1"
            >
              Project
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Briefcase className="h-5 w-5 text-text-secondary" />
              </div>
              <select
                id="projectId"
                name="projectId"
                value={formData.projectId}
                onChange={handleInputChange}
                className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full pl-10 p-2.5"
                required
              >
                <option value="">Select Project</option>
                {mockProjects.map((project) => (
                  <option key={project.id} value={project.id}>
                    {project.title}
                  </option>
                ))}
              </select>
            </div>
          </div>
        )}

        {/* Evaluation Type */}
        <div>
          <label
            htmlFor="type"
            className="block text-sm font-medium text-white mb-1"
          >
            Evaluation Type
          </label>
          <select
            id="type"
            name="type"
            value={formData.type}
            onChange={handleInputChange}
            className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
          >
            <option value="periodic">Periodic Evaluation</option>
            <option value="project">Project Review</option>
          </select>
        </div>

        {/* Evaluation Date */}
        <div>
          <label
            htmlFor="date"
            className="block text-sm font-medium text-white mb-1"
          >
            Evaluation Date
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Calendar className="h-5 w-5 text-text-secondary" />
            </div>
            <input
              type="date"
              id="date"
              name="date"
              value={formData.date}
              onChange={handleInputChange}
              className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full pl-10 p-2.5"
              required
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 mt-6">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 btn-white-ghost font-medium rounded-lg text-sm"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 btn-white font-medium rounded-lg text-sm"
          >
            Create Evaluation
          </button>
        </div>
      </form>
    </Modal>
  );
};

export default CreateEvaluationModal;
