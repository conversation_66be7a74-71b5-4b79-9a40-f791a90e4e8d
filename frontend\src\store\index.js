import { configureStore } from '@reduxjs/toolkit';
import authSlice from './slices/authSlice';
import companiesSlice from './slices/companiesSlice';
import usersSlice from './slices/usersSlice';
import projectsSlice from './slices/projectsSlice';
import tasksSlice from './slices/tasksSlice';
import attendanceSlice from './slices/attendanceSlice';
import leavesSlice from './slices/leavesSlice';
import evaluationsSlice from './slices/evaluationsSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    companies: companiesSlice,
    users: usersSlice,
    projects: projectsSlice,
    tasks: tasksSlice,
    attendance: attendanceSlice,
    leaves: leavesSlice,
    evaluations: evaluationsSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});
