import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Mail, CheckCircle, AlertCircle, ArrowRight } from 'lucide-react';
import OnboardingLayout from '../../components/onboarding/OnboardingLayout';

const EmailVerificationPage = () => {
  const navigate = useNavigate();
  const [verificationState, setVerificationState] = useState('pending'); // pending, success, error
  const [email, setEmail] = useState('<EMAIL>'); // This would come from auth context in a real app
  const [countdown, setCountdown] = useState(0);
  
  // Simulate verification process
  useEffect(() => {
    // In a real app, you would check if the email is verified via an API call
    const timer = setTimeout(() => {
      setVerificationState('success');
    }, 3000);
    
    return () => clearTimeout(timer);
  }, []);
  
  // Countdown for resend button
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);
  
  const handleResendEmail = () => {
    // In a real app, you would call an API to resend the verification email
    setCountdown(60); // Disable resend button for 60 seconds
  };
  
  const handleContinue = () => {
    navigate('/onboarding/company');
  };
  
  return (
    <OnboardingLayout currentStep={1}>
      <div className="text-center">
        <div className="mx-auto w-16 h-16 flex items-center justify-center rounded-full bg-surface-3 mb-6">
          {verificationState === 'pending' && <Mail size={28} className="text-soft-blue" />}
          {verificationState === 'success' && <CheckCircle size={28} className="text-accent-2" />}
          {verificationState === 'error' && <AlertCircle size={28} className="text-accent-4" />}
        </div>
        
        <h2 className="text-xl font-semibold text-white mb-2">
          {verificationState === 'pending' && 'Verify Your Email'}
          {verificationState === 'success' && 'Email Verified!'}
          {verificationState === 'error' && 'Verification Failed'}
        </h2>
        
        <p className="text-text-secondary mb-6">
          {verificationState === 'pending' && (
            <>We've sent a verification link to <span className="text-white">{email}</span></>
          )}
          {verificationState === 'success' && 'Your email has been successfully verified.'}
          {verificationState === 'error' && 'We couldn\'t verify your email. Please try again.'}
        </p>
        
        {verificationState === 'pending' && (
          <div className="space-y-4">
            <div className="flex items-center justify-center space-x-2">
              <div className="w-2 h-2 rounded-full bg-soft-blue animate-pulse"></div>
              <div className="w-2 h-2 rounded-full bg-soft-blue animate-pulse delay-100"></div>
              <div className="w-2 h-2 rounded-full bg-soft-blue animate-pulse delay-200"></div>
            </div>
            <p className="text-sm text-text-secondary">
              This may take a moment. Please check your inbox and click the verification link.
            </p>
            <div className="mt-4">
              <button
                onClick={handleResendEmail}
                disabled={countdown > 0}
                className="text-soft-blue hover:text-white disabled:text-text-secondary transition-colors text-sm"
              >
                {countdown > 0 ? `Resend email (${countdown}s)` : 'Resend verification email'}
              </button>
            </div>
          </div>
        )}
        
        {verificationState === 'success' && (
          <button
            onClick={handleContinue}
            className="mt-4 px-6 py-2.5 btn-white font-medium rounded-lg text-sm inline-flex items-center"
          >
            Continue to Company Setup
            <ArrowRight size={16} className="ml-2" />
          </button>
        )}
        
        {verificationState === 'error' && (
          <div className="space-y-4">
            <p className="text-sm text-text-secondary">
              Please check your email address and try again.
            </p>
            <button
              onClick={handleResendEmail}
              disabled={countdown > 0}
              className="px-6 py-2.5 btn-white-ghost font-medium rounded-lg text-sm"
            >
              {countdown > 0 ? `Resend email (${countdown}s)` : 'Resend verification email'}
            </button>
          </div>
        )}
      </div>
    </OnboardingLayout>
  );
};

export default EmailVerificationPage;
