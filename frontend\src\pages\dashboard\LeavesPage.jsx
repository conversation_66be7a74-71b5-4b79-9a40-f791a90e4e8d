import { useState } from "react";
import {
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  Filter,
  Search,
  ChevronDown,
  ChevronRight,
  Download,
  CalendarDays,
  ListFilter,
  MessageSquare,
  Info,
} from "lucide-react";

// Mock leave data
const mockLeaves = [
  {
    id: 1,
    employee: {
      id: 1,
      name: "<PERSON>",
      department: "Engineering",
    },
    type: "Vacation",
    startDate: "2023-07-15",
    endDate: "2023-07-20",
    status: "Approved",
    requestedOn: "2023-06-25",
    approvedBy: "<PERSON>",
    reason: "Annual family vacation",
  },
  {
    id: 2,
    employee: {
      id: 2,
      name: "<PERSON>",
      department: "Design",
    },
    type: "Sick",
    startDate: "2023-07-05",
    endDate: "2023-07-06",
    status: "Approved",
    requestedOn: "2023-07-04",
    approvedBy: "<PERSON>",
    reason: "Not feeling well",
  },
  {
    id: 3,
    employee: {
      id: 3,
      name: "<PERSON>",
      department: "Product",
    },
    type: "Personal",
    startDate: "2023-07-25",
    endDate: "2023-07-26",
    status: "Pending",
    requestedOn: "2023-07-10",
    approvedBy: null,
    reason: "Family matters",
  },
  {
    id: 4,
    employee: {
      id: 4,
      name: "<PERSON> <PERSON>",
      department: "HR",
    },
    type: "Vacation",
    startDate: "2023-08-10",
    endDate: "2023-08-15",
    status: "Pending",
    requestedOn: "2023-07-15",
    approvedBy: null,
    reason: "Summer vacation",
  },
  {
    id: 5,
    employee: {
      id: 5,
      name: "Michael Wilson",
      department: "Engineering",
    },
    type: "Sick",
    startDate: "2023-07-03",
    endDate: "2023-07-03",
    status: "Rejected",
    requestedOn: "2023-07-02",
    approvedBy: "Sarah Brown",
    reason: "Doctor appointment",
  },
  {
    id: 6,
    employee: {
      id: 6,
      name: "Sarah Brown",
      department: "Finance",
    },
    type: "Bereavement",
    startDate: "2023-07-12",
    endDate: "2023-07-14",
    status: "Approved",
    requestedOn: "2023-07-11",
    approvedBy: "Robert Johnson",
    reason: "Family emergency",
  },
  {
    id: 7,
    employee: {
      id: 7,
      name: "David Miller",
      department: "Product",
    },
    type: "Vacation",
    startDate: "2023-08-20",
    endDate: "2023-08-27",
    status: "Pending",
    requestedOn: "2023-07-20",
    approvedBy: null,
    reason: "Family trip",
  },
  {
    id: 8,
    employee: {
      id: 8,
      name: "Lisa Taylor",
      department: "Engineering",
    },
    type: "Personal",
    startDate: "2023-07-28",
    endDate: "2023-07-28",
    status: "Approved",
    requestedOn: "2023-07-21",
    approvedBy: "Sarah Brown",
    reason: "Personal matters",
  },
];

// Mock employees for filter
const mockEmployees = [
  { id: 1, name: "John Doe", department: "Engineering" },
  { id: 2, name: "Jane Smith", department: "Design" },
  { id: 3, name: "Robert Johnson", department: "Product" },
  { id: 4, name: "Emily Davis", department: "HR" },
  { id: 5, name: "Michael Wilson", department: "Engineering" },
  { id: 6, name: "Sarah Brown", department: "Finance" },
  { id: 7, name: "David Miller", department: "Product" },
  { id: 8, name: "Lisa Taylor", department: "Engineering" },
];

// Leave types for filter
const leaveTypes = ["Vacation", "Sick", "Personal", "Bereavement", "Other"];

// Leave statuses for filter
const leaveStatuses = ["Pending", "Approved", "Rejected"];

const LeavesPage = () => {
  const [activeTab, setActiveTab] = useState("upcoming");
  const [leaves, setLeaves] = useState(mockLeaves);
  const [searchTerm, setSearchTerm] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [expandedLeaves, setExpandedLeaves] = useState({});
  const [filters, setFilters] = useState({
    employee: null,
    type: null,
    status: null,
    dateRange: {
      start: "",
      end: "",
    },
  });

  // Get current date for filtering upcoming leaves
  const currentDate = new Date();
  currentDate.setHours(0, 0, 0, 0);

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Calculate duration in days
  const calculateDuration = (startDate, endDate) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
    return diffDays;
  };

  // Filter leaves based on active tab and search/filters
  const filteredLeaves = leaves.filter((leave) => {
    // First filter by tab
    if (activeTab === "upcoming") {
      const leaveStartDate = new Date(leave.startDate);
      return leaveStartDate >= currentDate && leave.status === "Approved";
    } else if (activeTab === "pending") {
      return leave.status === "Pending";
    } else if (activeTab === "all") {
      // All leaves, continue with other filters
    }

    // Then apply search and filters
    const matchesSearch =
      leave.employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      leave.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      leave.reason.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesEmployeeFilter =
      !filters.employee || leave.employee.id === filters.employee;
    const matchesTypeFilter = !filters.type || leave.type === filters.type;
    const matchesStatusFilter =
      !filters.status || leave.status === filters.status;

    let matchesDateFilter = true;
    if (filters.dateRange.start && filters.dateRange.end) {
      const leaveStart = new Date(leave.startDate);
      const leaveEnd = new Date(leave.endDate);
      const filterStart = new Date(filters.dateRange.start);
      const filterEnd = new Date(filters.dateRange.end);

      matchesDateFilter =
        (leaveStart >= filterStart && leaveStart <= filterEnd) ||
        (leaveEnd >= filterStart && leaveEnd <= filterEnd);
    }

    return (
      matchesSearch &&
      matchesEmployeeFilter &&
      matchesTypeFilter &&
      matchesStatusFilter &&
      matchesDateFilter
    );
  });

  // Reset all filters
  const resetFilters = () => {
    setFilters({
      employee: null,
      type: null,
      status: null,
      dateRange: {
        start: "",
        end: "",
      },
    });
    setSearchTerm("");
  };

  // Export leaves as CSV
  const exportLeaves = () => {
    const headers = [
      "Employee",
      "Department",
      "Type",
      "Start Date",
      "End Date",
      "Duration",
      "Status",
      "Requested On",
    ];
    const csvData = [
      headers.join(","),
      ...filteredLeaves.map((leave) =>
        [
          leave.employee.name,
          leave.employee.department,
          leave.type,
          leave.startDate,
          leave.endDate,
          calculateDuration(leave.startDate, leave.endDate),
          leave.status,
          leave.requestedOn,
        ].join(",")
      ),
    ].join("\n");

    const blob = new Blob([csvData], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `leaves-${activeTab}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  // Handle leave approval/rejection
  const handleLeaveAction = (id, action) => {
    setLeaves(
      leaves.map((leave) => {
        if (leave.id === id) {
          return {
            ...leave,
            status: action === "approve" ? "Approved" : "Rejected",
            approvedBy: "Current User", // In a real app, this would be the current user's name
          };
        }
        return leave;
      })
    );
  };

  // Toggle leave details expansion
  const toggleLeaveDetails = (id) => {
    setExpandedLeaves((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Leave Management</h2>
          <p className="mt-1 text-sm text-text-secondary">
            Manage leave requests and approvals
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex space-x-3">
          <button
            onClick={exportLeaves}
            className="px-4 py-2 btn-white-ghost font-medium rounded-lg text-sm flex items-center"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-border">
        <div className="flex overflow-x-auto hide-scrollbar">
          <nav className="flex space-x-1" aria-label="Leave Tabs">
            <button
              onClick={() => setActiveTab("upcoming")}
              className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                activeTab === "upcoming"
                  ? "border-accent-1 text-white"
                  : "border-transparent text-text-secondary hover:text-white hover:border-border"
              }`}
            >
              <CalendarDays className="h-4 w-4 inline mr-2" />
              Upcoming Leaves
            </button>

            <button
              onClick={() => setActiveTab("pending")}
              className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                activeTab === "pending"
                  ? "border-accent-1 text-white"
                  : "border-transparent text-text-secondary hover:text-white hover:border-border"
              }`}
            >
              <Clock className="h-4 w-4 inline mr-2" />
              Pending Requests
            </button>

            <button
              onClick={() => setActiveTab("all")}
              className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                activeTab === "all"
                  ? "border-accent-1 text-white"
                  : "border-transparent text-text-secondary hover:text-white hover:border-border"
              }`}
            >
              <ListFilter className="h-4 w-4 inline mr-2" />
              All Leaves
            </button>
          </nav>
        </div>
      </div>

      {/* Search and filter */}
      <div className="glass-card p-4 rounded-lg">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-4 w-4 text-text-secondary" />
            </div>
            <input
              type="text"
              placeholder="Search by employee, leave type, or reason..."
              className="input-dark pl-10 pr-4 py-2 w-full rounded-lg"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex items-center space-x-3">
            <button
              className="px-3 py-2 btn-white-ghost font-medium rounded-lg text-sm flex items-center"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
              <ChevronDown
                className={`h-4 w-4 ml-2 transition-transform ${
                  showFilters ? "rotate-180" : ""
                }`}
              />
            </button>
            {(filters.employee ||
              filters.type ||
              filters.status ||
              filters.dateRange.start) && (
              <button
                className="px-3 py-2 text-text-secondary hover:text-white text-sm"
                onClick={resetFilters}
              >
                Reset
              </button>
            )}
          </div>
        </div>

        {/* Filter options */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-border grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label
                htmlFor="employeeFilter"
                className="block text-sm font-medium text-white mb-1"
              >
                Employee
              </label>
              <select
                id="employeeFilter"
                className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                value={filters.employee || ""}
                onChange={(e) =>
                  setFilters({
                    ...filters,
                    employee: e.target.value ? Number(e.target.value) : null,
                  })
                }
              >
                <option value="">All Employees</option>
                {mockEmployees.map((employee) => (
                  <option key={employee.id} value={employee.id}>
                    {employee.name} ({employee.department})
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label
                htmlFor="typeFilter"
                className="block text-sm font-medium text-white mb-1"
              >
                Leave Type
              </label>
              <select
                id="typeFilter"
                className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                value={filters.type || ""}
                onChange={(e) =>
                  setFilters({ ...filters, type: e.target.value || null })
                }
              >
                <option value="">All Types</option>
                {leaveTypes.map((type) => (
                  <option key={type} value={type}>
                    {type}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label
                htmlFor="statusFilter"
                className="block text-sm font-medium text-white mb-1"
              >
                Status
              </label>
              <select
                id="statusFilter"
                className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                value={filters.status || ""}
                onChange={(e) =>
                  setFilters({ ...filters, status: e.target.value || null })
                }
              >
                <option value="">All Statuses</option>
                {leaveStatuses.map((status) => (
                  <option key={status} value={status}>
                    {status}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-white mb-1">
                Date Range
              </label>
              <div className="flex space-x-2">
                <input
                  type="date"
                  className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                  value={filters.dateRange.start}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      dateRange: {
                        ...filters.dateRange,
                        start: e.target.value,
                      },
                    })
                  }
                />
                <input
                  type="date"
                  className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                  value={filters.dateRange.end}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      dateRange: { ...filters.dateRange, end: e.target.value },
                    })
                  }
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Leaves table */}
      <div className="glass-card rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-surface-2 border-b border-border">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Employee
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Leave Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Duration
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Requested On
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-border">
              {filteredLeaves.length > 0 ? (
                filteredLeaves.map((leave) => (
                  <>
                    <tr
                      key={leave.id}
                      className="hover:bg-surface-2 cursor-pointer"
                      onClick={() => toggleLeaveDetails(leave.id)}
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-white">
                          {leave.employee.name}
                        </div>
                        <div className="text-xs text-text-secondary">
                          {leave.employee.department}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-white">{leave.type}</div>
                        <div className="text-xs text-text-secondary flex items-center">
                          <MessageSquare className="h-3 w-3 mr-1" />
                          <span className="truncate max-w-[150px]">
                            {leave.reason}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-white">
                          {formatDate(leave.startDate)} -{" "}
                          {formatDate(leave.endDate)}
                        </div>
                        <div className="text-xs text-text-secondary">
                          {calculateDuration(leave.startDate, leave.endDate)}{" "}
                          day(s)
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-2 py-1 text-xs rounded-full ${
                            leave.status === "Approved"
                              ? "bg-green-900/30 text-green-300"
                              : leave.status === "Pending"
                              ? "bg-blue-900/30 text-blue-300"
                              : "bg-red-900/30 text-red-300"
                          }`}
                        >
                          {leave.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-white">
                          {formatDate(leave.requestedOn)}
                        </div>
                        {leave.approvedBy && (
                          <div className="text-xs text-text-secondary">
                            {leave.status === "Approved"
                              ? "Approved by"
                              : "Rejected by"}
                            : {leave.approvedBy}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right">
                        <div className="flex justify-end space-x-2">
                          {leave.status === "Pending" ? (
                            <>
                              <button
                                className="p-1 text-green-400 hover:text-green-300"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleLeaveAction(leave.id, "approve");
                                }}
                              >
                                <CheckCircle size={16} />
                              </button>
                              <button
                                className="p-1 text-red-400 hover:text-red-300"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleLeaveAction(leave.id, "reject");
                                }}
                              >
                                <XCircle size={16} />
                              </button>
                            </>
                          ) : (
                            <button
                              className="p-1 text-text-secondary hover:text-white"
                              onClick={(e) => {
                                e.stopPropagation();
                                toggleLeaveDetails(leave.id);
                              }}
                            >
                              <Info size={16} />
                            </button>
                          )}
                          <ChevronRight
                            size={16}
                            className={`text-text-secondary transition-transform ${
                              expandedLeaves[leave.id] ? "rotate-90" : ""
                            }`}
                          />
                        </div>
                      </td>
                    </tr>
                    {expandedLeaves[leave.id] && (
                      <tr className="bg-surface-2">
                        <td colSpan="6" className="px-6 py-4">
                          <div className="glass-card p-4 rounded-lg">
                            <div className="mb-4">
                              <h4 className="text-sm font-medium text-white mb-2">
                                Leave Details
                              </h4>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                  <h5 className="text-xs font-medium text-text-secondary mb-1">
                                    Reason for Leave
                                  </h5>
                                  <p className="text-sm text-white">
                                    {leave.reason}
                                  </p>
                                </div>
                                {leave.status !== "Pending" && (
                                  <div>
                                    <h5 className="text-xs font-medium text-text-secondary mb-1">
                                      {leave.status === "Approved"
                                        ? "Approval"
                                        : "Rejection"}{" "}
                                      Information
                                    </h5>
                                    <p className="text-sm text-white">
                                      {leave.status === "Approved"
                                        ? `Approved by ${
                                            leave.approvedBy
                                          } on ${formatDate(leave.requestedOn)}`
                                        : `Rejected by ${
                                            leave.approvedBy
                                          } on ${formatDate(
                                            leave.requestedOn
                                          )}`}
                                    </p>
                                    <p className="text-xs text-text-secondary mt-1">
                                      {leave.status === "Approved"
                                        ? "Your leave request has been approved. Enjoy your time off!"
                                        : "Your leave request has been rejected. Please contact your manager for more information."}
                                    </p>
                                  </div>
                                )}
                              </div>
                            </div>
                            <div className="flex justify-end">
                              <button
                                className="px-3 py-1 text-xs btn-white-ghost rounded-lg"
                                onClick={() => toggleLeaveDetails(leave.id)}
                              >
                                Close
                              </button>
                            </div>
                          </div>
                        </td>
                      </tr>
                    )}
                  </>
                ))
              ) : (
                <tr>
                  <td
                    colSpan="6"
                    className="px-6 py-4 text-center text-sm text-text-secondary"
                  >
                    No leaves found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default LeavesPage;
