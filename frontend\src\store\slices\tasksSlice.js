import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { tasksService } from '../../api';

// Async thunks for tasks operations
export const fetchTasks = createAsyncThunk(
  'tasks/fetchTasks',
  async (_, { rejectWithValue }) => {
    try {
      const response = await tasksService.getTasks();
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to fetch tasks'
      );
    }
  }
);

export const createTask = createAsyncThunk(
  'tasks/createTask',
  async (taskData, { rejectWithValue }) => {
    try {
      const response = await tasksService.createTask(taskData);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to create task'
      );
    }
  }
);

export const fetchTaskById = createAsyncThunk(
  'tasks/fetchTaskById',
  async (taskId, { rejectWithValue }) => {
    try {
      const response = await tasksService.getTaskById(taskId);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to fetch task'
      );
    }
  }
);

export const updateTask = createAsyncThunk(
  'tasks/updateTask',
  async ({ id, taskData }, { rejectWithValue }) => {
    try {
      const response = await tasksService.updateTask(id, taskData);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to update task'
      );
    }
  }
);

export const deleteTask = createAsyncThunk(
  'tasks/deleteTask',
  async (taskId, { rejectWithValue }) => {
    try {
      await tasksService.deleteTask(taskId);
      return taskId;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to delete task'
      );
    }
  }
);

export const moveTask = createAsyncThunk(
  'tasks/moveTask',
  async ({ id, moveData }, { rejectWithValue }) => {
    try {
      const response = await tasksService.moveTask(id, moveData);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to move task'
      );
    }
  }
);

export const addTaskComment = createAsyncThunk(
  'tasks/addTaskComment',
  async ({ taskId, commentData }, { rejectWithValue }) => {
    try {
      const response = await tasksService.addTaskComment(taskId, commentData);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to add comment'
      );
    }
  }
);

export const addChecklistItem = createAsyncThunk(
  'tasks/addChecklistItem',
  async ({ taskId, checklistData }, { rejectWithValue }) => {
    try {
      const response = await tasksService.addChecklistItem(taskId, checklistData);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to add checklist item'
      );
    }
  }
);

export const updateChecklistItem = createAsyncThunk(
  'tasks/updateChecklistItem',
  async ({ taskId, itemId, updateData }, { rejectWithValue }) => {
    try {
      const response = await tasksService.updateChecklistItem(taskId, itemId, updateData);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to update checklist item'
      );
    }
  }
);

// Initial state
const initialState = {
  tasks: [],
  currentTask: null,
  loading: false,
  error: null,
  createLoading: false,
  updateLoading: false,
  deleteLoading: false,
  moveLoading: false,
  commentLoading: false,
  checklistLoading: false,
};

// Tasks slice
const tasksSlice = createSlice({
  name: 'tasks',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentTask: (state, action) => {
      state.currentTask = action.payload;
    },
    clearCurrentTask: (state) => {
      state.currentTask = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch tasks
    builder
      .addCase(fetchTasks.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTasks.fulfilled, (state, action) => {
        state.loading = false;
        state.tasks = action.payload.tasks || action.payload;
        state.error = null;
      })
      .addCase(fetchTasks.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
    // Create task
    builder
      .addCase(createTask.pending, (state) => {
        state.createLoading = true;
        state.error = null;
      })
      .addCase(createTask.fulfilled, (state, action) => {
        state.createLoading = false;
        state.tasks.push(action.payload.task || action.payload);
        state.error = null;
      })
      .addCase(createTask.rejected, (state, action) => {
        state.createLoading = false;
        state.error = action.payload;
      })
      
    // Fetch task by ID
    builder
      .addCase(fetchTaskById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTaskById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentTask = action.payload.task || action.payload;
        state.error = null;
      })
      .addCase(fetchTaskById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
    // Update task
    builder
      .addCase(updateTask.pending, (state) => {
        state.updateLoading = true;
        state.error = null;
      })
      .addCase(updateTask.fulfilled, (state, action) => {
        state.updateLoading = false;
        const updatedTask = action.payload.task || action.payload;
        const index = state.tasks.findIndex(task => task.id === updatedTask.id);
        if (index !== -1) {
          state.tasks[index] = updatedTask;
        }
        if (state.currentTask && state.currentTask.id === updatedTask.id) {
          state.currentTask = updatedTask;
        }
        state.error = null;
      })
      .addCase(updateTask.rejected, (state, action) => {
        state.updateLoading = false;
        state.error = action.payload;
      })
      
    // Delete task
    builder
      .addCase(deleteTask.pending, (state) => {
        state.deleteLoading = true;
        state.error = null;
      })
      .addCase(deleteTask.fulfilled, (state, action) => {
        state.deleteLoading = false;
        state.tasks = state.tasks.filter(task => task.id !== action.payload);
        if (state.currentTask && state.currentTask.id === action.payload) {
          state.currentTask = null;
        }
        state.error = null;
      })
      .addCase(deleteTask.rejected, (state, action) => {
        state.deleteLoading = false;
        state.error = action.payload;
      })
      
    // Move task
    builder
      .addCase(moveTask.pending, (state) => {
        state.moveLoading = true;
        state.error = null;
      })
      .addCase(moveTask.fulfilled, (state, action) => {
        state.moveLoading = false;
        const updatedTask = action.payload.task || action.payload;
        const index = state.tasks.findIndex(task => task.id === updatedTask.id);
        if (index !== -1) {
          state.tasks[index] = updatedTask;
        }
        if (state.currentTask && state.currentTask.id === updatedTask.id) {
          state.currentTask = updatedTask;
        }
        state.error = null;
      })
      .addCase(moveTask.rejected, (state, action) => {
        state.moveLoading = false;
        state.error = action.payload;
      })
      
    // Add task comment
    builder
      .addCase(addTaskComment.pending, (state) => {
        state.commentLoading = true;
        state.error = null;
      })
      .addCase(addTaskComment.fulfilled, (state, action) => {
        state.commentLoading = false;
        state.error = null;
      })
      .addCase(addTaskComment.rejected, (state, action) => {
        state.commentLoading = false;
        state.error = action.payload;
      })
      
    // Add checklist item
    builder
      .addCase(addChecklistItem.pending, (state) => {
        state.checklistLoading = true;
        state.error = null;
      })
      .addCase(addChecklistItem.fulfilled, (state, action) => {
        state.checklistLoading = false;
        state.error = null;
      })
      .addCase(addChecklistItem.rejected, (state, action) => {
        state.checklistLoading = false;
        state.error = action.payload;
      })
      
    // Update checklist item
    builder
      .addCase(updateChecklistItem.pending, (state) => {
        state.checklistLoading = true;
        state.error = null;
      })
      .addCase(updateChecklistItem.fulfilled, (state, action) => {
        state.checklistLoading = false;
        state.error = null;
      })
      .addCase(updateChecklistItem.rejected, (state, action) => {
        state.checklistLoading = false;
        state.error = action.payload;
      });
  },
});

export const { clearError, setCurrentTask, clearCurrentTask } = tasksSlice.actions;
export default tasksSlice.reducer;
