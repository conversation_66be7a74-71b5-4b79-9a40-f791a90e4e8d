Product Requirements Document: Team Management App v1
Executive Summary
Team Management App v1 is a simple, intuitive web-based platform with Slack integration to streamline team oversight for managers. It enables managers to monitor check-ins/check-outs, leaves, attendance, tasks, performance, and employee roles via a clean dashboard, while employees interact primarily through a Slack bot. The MVP focuses on core features—check-in/check-out, leave management, attendance tracking, Team Tasks, performance evaluation, employee management, and Slack integration—designed for ease of use and minimal complexity to differentiate in the market.
Problem Statement
Small to medium-sized companies lack simple, non-bloated tools to manage team activities like attendance, task progress, and performance without the complexity of full-scale project management platforms. Managers need a 360-degree view of their team’s status, while employees require an easy way to report their work and request leaves without navigating a web interface.
User Personas
Manager:
Needs: Centralized view of team check-ins, leaves, tasks, and performance; ability to set rules (e.g., check-in hours); approve leaves; assign tasks; evaluate employees.
Pain Points: Overly complex tools; lack of quick insights into team status.
Employee:
Needs: Simple way to check in/out, request leaves, and view task assignments via employee poral
Pain Points: Complicated interfaces; repetitive web logins.
Admin:
Needs: Set up company details, invite employees, assign roles.
Pain Points: Slow onboarding; unclear role management.
Goals and Success Metrics
Goals:
Provide managers with a clear, real-time team overview in under 5 seconds.
Enable employees to check in/out or request leaves via employee portal in under 10 seconds.
Deliver a lightweight task system that supports performance evaluations without complexity.
Success Metrics:
90% of managers can view team status without training.

App deployment completed by Q3 2025 with 80% feature coverage.
50% reduction in time spent on manual team tracking (e.g., emails, spreadsheets).
Functional Requirements
Check-In/Check-Out System:
Employees send Slack commands (/checkin, /checkout) to mark work start/end.
Check-in includes optional message (e.g., “/checkin Projects: Task A, Plan: Finish report”).
Managers set check-in/checkout hours and message formats (e.g., require project mention) via Settings.
Dashboard shows today’s check-in/checkout statuses for all employees, with timestamps and messages.
After-hours check-out enforced based on manager settings.
Leave Management:
Employees request leaves via Slack (/leave request 2025-05-20 2025-05-24 Vacation) or web form.
Leave model: employeeId, startDate, endDate, reason, status (pending/approved/denied), createdBy, approvedBy.
Managers view and approve/deny requests on dashboard; Slack bot notifies employees of status.
Dashboard shows today’s/month’s leaves and a calendar badge for upcoming leaves.
Managers set leave policies (e.g., max days, notice period) in Settings.
Attendance Tracking:
Automatically tracks check-ins/check-outs and leaves to mark attendance.
Dashboard shows daily team overview: Present, On Leave, Unresponsive (no check-in).
Monthly attendance summary available for managers.
Team Tasks:
Renamed from “Project Management” to emphasize simplicity while retaining a project-like structure.
Managers create Team Tasks with:
Title (e.g., “Website Redesign”).
Description (e.g., “Update company site UI”).
Assigned employees (multiple selections).
Status (Not Started, In Progress, Done).
Optional milestones or activities (e.g., “Design Phase,” “Testing”) as a simple list within the task, added during creation or later.
A single Team Task can include multiple sub-elements (milestones/activities) to track progress without complex hierarchies.
Employees view assigned tasks via Slack (/mystatus) or web dashboard (secondary, read-only view).
Optional Kanban-style view on dashboard to visualize task statuses (Not Started, In Progress, Done columns), to be evaluated during development to ensure it doesn’t compromise simplicity.
Minimal features: Focus on task creation, assignment, status updates, and milestone tracking; avoid advanced PM tools (e.g., Gantt charts, dependencies).
Tasks link to performance evaluations (e.g., rate employees based on task completion).
Performance Evaluation:
Managers rate employees (1–5) post-task completion, with optional feedback.
Evaluations tied to Team Tasks for context (e.g., “Rated 4 for Website Redesign”).
Dashboard shows employee performance history (average score, past ratings).
Evaluations occur periodically (e.g., quarterly), showing task history during review.
Employee Management:
Admins/Managers add employees, assign roles (Admin, Manager, Employee), and set work titles.
Dashboard table shows employee details: role, status (Present/On Leave), attendance history, past leaves, performance scores, assigned tasks.
Slack Integration:
Slack bot handles employee actions: /checkin, /checkout, /leave request, /leave status, /mystatus.
Bot responds with confirmations (e.g., “Check-in recorded at 9:00 AM”).
/mystatus shows employee’s check-in status, assigned Team Tasks, and task statuses.
Managers receive leave request notifications in Slack but approve via web (MVP).
Dashboard Layout:
Top Nav: Company name, user avatar, logout.
Sidebar: Dashboard, Team Tasks, Attendance, Employees, Settings.
Main Dashboard:
Today’s activity: Check-ins, performance snapshot (e.g., average team score).
Quick stats: Unresponsive employees, leave count, task completion rate.
Team Tasks progress: Summary of active tasks and statuses (table or optional Kanban view).
Non-Functional Requirements
Performance: Dashboard loads in < 2 seconds; Slack bot responds in < 1 second.
Scalability: Support 100 concurrent users (small/medium teams).
Security: JWT-based authentication; role-based access control; encrypted API calls.
Usability: 90% of users (managers/employees) complete core tasks without support.
Compatibility: Web app works on Chrome, Firefox, Safari; Slack bot on all Slack platforms.
Hosting: Deploy on ManaKnight Digital servers (if aligned with pm2.manaknightdigital.com) or AWS.
Scope and Assumptions
In Scope:
Web dashboard for managers/admins.
Slack bot for employee interactions.
Core features: check-in/check-out, leave management, attendance, Team Tasks, performance evaluation, employee management.
Out of Scope:
Mobile app.
Complex project management features (e.g., Gantt charts, resource allocation).
Real-time chat or video integration.
Assumptions:
Managers have Slack and web access; employees have Slack.
pm2.manaknightdigital.com APIs (if used) support task/employee data integration.
Development completes by Q3 2025 with a team of 3–5 developers.
Constraints
Budget: MVP must be lean, avoiding feature bloat.
Timeline: Complete by Q3 2025.
Tech Stack: Node.js + Express (backend), React + TypeScript (frontend), MongoDB (database), Slack API (integration).
Team Size: Small dev team, prioritizing simplicity.
User Stories
As a Manager:
I want to create a Team Task with milestones so I can track progress simply.
I want to see task statuses on a dashboard (table or Kanban) to monitor team work without complexity.
I want to assign tasks to multiple employees so they can collaborate on a project-like task.
As an Employee:
I want to see my assigned Team Tasks via /mystatus in Slack so I know my responsibilities.
I want to view task details (e.g., milestones) on the web if needed, without complex navigation.
As an Admin:
I want to ensure Team Tasks are simple to set up so managers adopt the system quickly.
Technical Details
Backend:
Node.js + Express for REST APIs.
MongoDB for data storage (employees, tasks, leaves, evaluations).
JWT for authentication.
Team Tasks Model:
Fields: taskId, title, description, assignedEmployees (array of employeeId), status (Not Started, In Progress, Done), milestones (array of { title, description, status }), createdBy, createdAt, updatedAt.
APIs:
POST /tasks/create: Create task with title, description, assignees, optional milestones.
PATCH /tasks/update/:id: Update task status or milestones.
GET /tasks/list: List all tasks with filters (e.g., status, assignee).
POST /tasks/assign: Assign employees to a task.
POST /tasks/milestone/add: Add milestone to a task.
Slack webhook for bot commands (/mystatus returns task list).
Frontend:
React + TypeScript for dashboard.
Tailwind CSS for styling (simple, clean UI).
Components:
TaskTable: Displays tasks with title, status, assignees.
TaskKanban (optional): Kanban board with Not Started, In Progress, Done columns, to be evaluated for simplicity.
TaskDetails: Shows task description, assignees, milestones.
TaskForm: Form for task creation with fields for title, description, assignees, milestones.
Employee task view: Read-only list of assigned tasks with milestones.
Slack Bot:
Slack Bolt framework for bot development.
/mystatus: Returns employee’s check-in status and assigned Team Tasks (e.g., “Task: Website Redesign, Status: In Progress, Milestones: Design (Done), Testing (Not Started)”).
Integration with pm2.manaknightdigital.com (if applicable):
If pm2 is your company’s system, reuse its APIs for task/employee data.
Confirm with ManaKnight Digital for API docs or data structures.
Acceptance Criteria
Check-In/Check-Out:
Employee can check in/out via Slack; dashboard shows statuses for all employees.
Managers can set check-in hours and message rules in Settings.
Leave Management:
Employee can request leave via Slack; manager can approve/deny on dashboard.
Dashboard shows today’s/month’s leaves with calendar badges.
Attendance:
Dashboard marks employees as Present, On Leave, or Unresponsive based on check-ins/leaves.
Team Tasks:
Managers can create tasks with title, description, assignees, status, and optional milestones.
A single task can include multiple milestones/activities, added during creation or later.
Employees can view assigned tasks via Slack (/mystatus) or web (read-only).
Dashboard shows task statuses in a table; optional Kanban view implemented only if it maintains simplicity.
Task creation and updates take < 10 seconds.
Performance Evaluation:
Managers can rate employees post-task; dashboard shows performance history.
Employee Management:
Admins can add employees and assign roles; dashboard shows full employee details.
Slack Integration:
All bot commands work reliably; responses are clear and instant.
Dashboard:
Loads in < 2 seconds, follows specified layout, and shows all required stats.

a settings page
Changelog
2025-05-16: Initial PRD draft for Team Management App v1.
2025-05-16 (01:44 AM WAT): Updated Team Tasks section to clarify simplicity, project-like structure, multiple sub-elements (milestones), and optional Kanban view.
