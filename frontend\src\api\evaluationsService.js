import axiosClient from './axiosClient';

const evaluationsService = {
  // Get evaluations
  getEvaluations: async (params = {}) => {
    try {
      const response = await axiosClient.get('/evaluations', { params });
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Create evaluation
  createEvaluation: async (evaluationData) => {
    try {
      const response = await axiosClient.post('/evaluations', evaluationData);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Get evaluation by ID
  getEvaluationById: async (id) => {
    try {
      const response = await axiosClient.get(`/evaluations/${id}`);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Update evaluation
  updateEvaluation: async (id, evaluationData) => {
    try {
      const response = await axiosClient.put(`/evaluations/${id}`, evaluationData);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Delete evaluation
  deleteEvaluation: async (id) => {
    try {
      const response = await axiosClient.delete(`/evaluations/${id}`);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Complete evaluation
  completeEvaluation: async (id, completionData) => {
    try {
      const response = await axiosClient.put(`/evaluations/${id}/complete`, completionData);
      return response;
    } catch (error) {
      throw error;
    }
  },
};

export default evaluationsService;
