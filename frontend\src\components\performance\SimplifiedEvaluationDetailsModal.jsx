import { useState, useEffect } from "react";
import {
  Calendar,
  Users,
  CheckSquare,
  AlignLeft,
  Edit,
  Save,
  Trash2,
  Clock,
  Briefcase,
  CheckCircle,
  XCircle,
  AlertCircle,
} from "lucide-react";
import Modal from "../common/Modal";

const SimplifiedEvaluationDetailsModal = ({
  isOpen,
  onClose,
  evaluation,
  onUpdateEvaluation,
  onDeleteEvaluation,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedEvaluation, setEditedEvaluation] = useState(evaluation || {});
  const [activeTab, setActiveTab] = useState("overview");

  // Mock data for employee details
  const mockEmployeeDetails = {
    1: {
      name: "<PERSON>",
      position: "Frontend Developer",
      department: "Engineering",
      dateJoined: "2023-01-15",
      attendance: { present: 180, expected: 200 },
      leaves: { taken: 12, total: 24 },
      projects: [
        { id: 1, title: "Website Redesign", rating: 8 },
        { id: 2, title: "Mobile App Development", rating: 7 },
      ],
      previousEvaluations: [
        { id: 101, date: "2023-06-15", rating: 8, type: "periodic" },
        { id: 102, date: "2023-03-10", rating: 7, type: "periodic" },
      ],
    },
    2: {
      name: "<PERSON>",
      position: "UX Designer",
      department: "Design",
      dateJoined: "2022-08-10",
      attendance: { present: 190, expected: 200 },
      leaves: { taken: 8, total: 24 },
      projects: [
        { id: 1, title: "Website Redesign", rating: 9 },
        { id: 3, title: "Client Onboarding Improvement", rating: 9 },
      ],
      previousEvaluations: [
        { id: 103, date: "2023-06-15", rating: 9, type: "periodic" },
        { id: 104, date: "2023-03-10", rating: 8, type: "periodic" },
      ],
    },
  };

  // Update edited evaluation when the prop changes
  useEffect(() => {
    if (evaluation && evaluation.id !== editedEvaluation?.id) {
      setEditedEvaluation(evaluation);
    }
  }, [evaluation]);

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setEditedEvaluation({
      ...editedEvaluation,
      [name]: value,
    });
  };

  // Handle rating change (1-10 scale)
  const handleRatingChange = (rating) => {
    setEditedEvaluation({
      ...editedEvaluation,
      rating,
    });
  };

  // Handle save
  const handleSave = () => {
    onUpdateEvaluation(editedEvaluation);
    setIsEditing(false);
  };

  // Handle delete
  const handleDelete = () => {
    onDeleteEvaluation(editedEvaluation.id);
    onClose();
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // Get employee details
  const getEmployeeDetails = (employeeId) => {
    return mockEmployeeDetails[employeeId] || null;
  };

  // Render rating (1-10 scale)
  const renderRating = (rating, editable = false) => {
    return (
      <div className="flex flex-wrap items-center gap-1">
        {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((star) => (
          <button
            key={star}
            type="button"
            onClick={editable ? () => handleRatingChange(star) : undefined}
            className={`w-8 h-8 rounded-full flex items-center justify-center focus:outline-none ${
              star <= (rating || 0)
                ? "bg-soft-blue text-white"
                : "bg-surface-3 text-text-secondary"
            } ${!editable ? "cursor-default" : ""}`}
            disabled={!editable}
          >
            {star}
          </button>
        ))}
        <span className="ml-2 text-white">{rating || 0}/10</span>
      </div>
    );
  };

  if (!evaluation) return null;

  // Get employee details
  const employeeDetails = getEmployeeDetails(evaluation.employeeId);

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        evaluation.type === "periodic"
          ? "Periodic Evaluation Details"
          : "Project Review Details"
      }
      size="xl"
    >
      <div className="flex flex-col h-full">
        {/* Tabs */}
        <div className="flex border-b border-border mb-4 overflow-x-auto">
          <button
            className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
              activeTab === "overview"
                ? "border-accent-1 text-white"
                : "border-transparent text-text-secondary hover:text-white"
            }`}
            onClick={() => setActiveTab("overview")}
          >
            Overview
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
              activeTab === "performance"
                ? "border-accent-1 text-white"
                : "border-transparent text-text-secondary hover:text-white"
            }`}
            onClick={() => setActiveTab("performance")}
          >
            Performance Metrics
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
              activeTab === "history"
                ? "border-accent-1 text-white"
                : "border-transparent text-text-secondary hover:text-white"
            }`}
            onClick={() => setActiveTab("history")}
          >
            Evaluation History
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
              activeTab === "projects"
                ? "border-accent-1 text-white"
                : "border-transparent text-text-secondary hover:text-white"
            }`}
            onClick={() => setActiveTab("projects")}
          >
            Project History
          </button>
        </div>

        {activeTab === "overview" && (
          <div className="space-y-6">
            {/* Employee Info */}
            <div className="glass-card rounded-lg p-4">
              <h3 className="text-lg font-medium text-white mb-3">Employee Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-text-secondary text-sm">Name</p>
                  <p className="text-white font-medium">
                    {employeeDetails?.name || `Employee ${evaluation.employeeId}`}
                  </p>
                </div>
                <div>
                  <p className="text-text-secondary text-sm">Position</p>
                  <p className="text-white font-medium">
                    {employeeDetails?.position || "N/A"}
                  </p>
                </div>
                <div>
                  <p className="text-text-secondary text-sm">Department</p>
                  <p className="text-white font-medium">
                    {employeeDetails?.department || "N/A"}
                  </p>
                </div>
                <div>
                  <p className="text-text-secondary text-sm">Date Joined</p>
                  <p className="text-white font-medium">
                    {employeeDetails?.dateJoined
                      ? formatDate(employeeDetails.dateJoined)
                      : "N/A"}
                  </p>
                </div>
              </div>
            </div>

            {/* Evaluation Details */}
            <div className="glass-card rounded-lg p-4">
              <div className="flex justify-between items-center mb-3">
                <h3 className="text-lg font-medium text-white">Evaluation Details</h3>
                <div className="text-sm text-text-secondary">
                  {formatDate(evaluation.date)}
                </div>
              </div>

              {isEditing ? (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-white mb-1">
                      Performance Rating (1-10)
                    </label>
                    {renderRating(editedEvaluation.rating, true)}
                  </div>

                  <div>
                    <label htmlFor="strengths" className="block text-sm font-medium text-white mb-1">
                      Strengths
                    </label>
                    <textarea
                      id="strengths"
                      name="strengths"
                      value={editedEvaluation.strengths || ""}
                      onChange={handleInputChange}
                      rows="2"
                      className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
                    ></textarea>
                  </div>

                  <div>
                    <label htmlFor="improvements" className="block text-sm font-medium text-white mb-1">
                      Areas for Improvement
                    </label>
                    <textarea
                      id="improvements"
                      name="improvements"
                      value={editedEvaluation.improvements || ""}
                      onChange={handleInputChange}
                      rows="2"
                      className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
                    ></textarea>
                  </div>

                  <div>
                    <label htmlFor="feedback" className="block text-sm font-medium text-white mb-1">
                      Overall Feedback
                    </label>
                    <textarea
                      id="feedback"
                      name="feedback"
                      value={editedEvaluation.feedback || ""}
                      onChange={handleInputChange}
                      rows="3"
                      className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
                    ></textarea>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div>
                    <p className="text-text-secondary text-sm mb-1">Performance Rating</p>
                    {renderRating(evaluation.rating)}
                  </div>

                  <div>
                    <p className="text-text-secondary text-sm mb-1">Strengths</p>
                    <p className="text-white bg-surface-3 p-2.5 rounded-lg">
                      {evaluation.strengths || "No strengths specified."}
                    </p>
                  </div>

                  <div>
                    <p className="text-text-secondary text-sm mb-1">Areas for Improvement</p>
                    <p className="text-white bg-surface-3 p-2.5 rounded-lg">
                      {evaluation.improvements || "No areas for improvement specified."}
                    </p>
                  </div>

                  <div>
                    <p className="text-text-secondary text-sm mb-1">Overall Feedback</p>
                    <p className="text-white bg-surface-3 p-2.5 rounded-lg">
                      {evaluation.feedback || "No feedback provided."}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Action buttons */}
            <div className="flex justify-end space-x-3">
              {isEditing ? (
                <>
                  <button
                    type="button"
                    onClick={() => setIsEditing(false)}
                    className="px-4 py-2 btn-white-ghost font-medium rounded-lg text-sm"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={handleSave}
                    className="px-4 py-2 btn-white font-medium rounded-lg text-sm flex items-center"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    Save Changes
                  </button>
                </>
              ) : (
                <>
                  <button
                    type="button"
                    onClick={handleDelete}
                    className="px-4 py-2 bg-red-500 bg-opacity-20 text-red-400 hover:bg-opacity-30 font-medium rounded-lg text-sm flex items-center"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </button>
                  <button
                    type="button"
                    onClick={() => setIsEditing(true)}
                    className="px-4 py-2 btn-white font-medium rounded-lg text-sm flex items-center"
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </button>
                </>
              )}
            </div>
          </div>
        )}

        {activeTab === "performance" && employeeDetails && (
          <div className="space-y-6">
            {/* Attendance Stats */}
            <div className="glass-card rounded-lg p-4">
              <h3 className="text-lg font-medium text-white mb-3">Attendance Statistics</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-text-secondary text-sm">Present Days</p>
                  <div className="flex items-center">
                    <p className="text-white font-medium text-2xl">
                      {employeeDetails.attendance.present}
                    </p>
                    <p className="text-text-secondary text-sm ml-2">
                      / {employeeDetails.attendance.expected} expected
                    </p>
                  </div>
                  <div className="w-full bg-surface-3 rounded-full h-2 mt-2">
                    <div
                      className="bg-soft-blue h-2 rounded-full"
                      style={{
                        width: `${(employeeDetails.attendance.present / employeeDetails.attendance.expected) * 100}%`,
                      }}
                    ></div>
                  </div>
                </div>
                <div>
                  <p className="text-text-secondary text-sm">Leave Usage</p>
                  <div className="flex items-center">
                    <p className="text-white font-medium text-2xl">
                      {employeeDetails.leaves.taken}
                    </p>
                    <p className="text-text-secondary text-sm ml-2">
                      / {employeeDetails.leaves.total} available
                    </p>
                  </div>
                  <div className="w-full bg-surface-3 rounded-full h-2 mt-2">
                    <div
                      className="bg-soft-blue h-2 rounded-full"
                      style={{
                        width: `${(employeeDetails.leaves.taken / employeeDetails.leaves.total) * 100}%`,
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>

            {/* Projects Completed */}
            <div className="glass-card rounded-lg p-4">
              <h3 className="text-lg font-medium text-white mb-3">Projects Completed</h3>
              <p className="text-white font-medium text-2xl">
                {employeeDetails.projects.length}
              </p>
            </div>
          </div>
        )}

        {activeTab === "history" && employeeDetails && (
          <div className="glass-card rounded-lg p-4">
            <h3 className="text-lg font-medium text-white mb-3">Previous Evaluations</h3>
            {employeeDetails.previousEvaluations.length > 0 ? (
              <div className="space-y-4">
                {employeeDetails.previousEvaluations.map((prevEval) => (
                  <div
                    key={prevEval.id}
                    className="p-3 bg-surface-3 rounded-lg border border-border"
                  >
                    <div className="flex justify-between items-center mb-2">
                      <div className="text-white font-medium">
                        {prevEval.type === "periodic" ? "Periodic Review" : "Project Review"}
                      </div>
                      <div className="text-text-secondary text-sm">
                        {formatDate(prevEval.date)}
                      </div>
                    </div>
                    <div className="flex items-center">
                      <div className="text-text-secondary text-sm mr-2">Rating:</div>
                      <div className="text-white font-medium">{prevEval.rating}/10</div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-text-secondary">No previous evaluations found.</p>
            )}
          </div>
        )}

        {activeTab === "projects" && employeeDetails && (
          <div className="glass-card rounded-lg p-4">
            <h3 className="text-lg font-medium text-white mb-3">Project History</h3>
            {employeeDetails.projects.length > 0 ? (
              <div className="space-y-4">
                {employeeDetails.projects.map((project) => (
                  <div
                    key={project.id}
                    className="p-3 bg-surface-3 rounded-lg border border-border"
                  >
                    <div className="flex justify-between items-center mb-2">
                      <div className="text-white font-medium">{project.title}</div>
                      <div className="text-text-secondary text-sm">
                        Rating: {project.rating}/10
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-text-secondary">No project history found.</p>
            )}
          </div>
        )}
      </div>
    </Modal>
  );
};

export default SimplifiedEvaluationDetailsModal;
