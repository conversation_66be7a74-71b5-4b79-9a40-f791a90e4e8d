/**
 * Utility to check if the backend server is running
 */
export const checkBackendStatus = async () => {
  try {
    const response = await fetch(`${import.meta.env.VITE_BACKEND_URL}/auth/me`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    // If we get any response (even 401), the backend is running
    if (response.status === 401) {
      console.log('✅ Backend is running (401 expected for unauthenticated request)');
      return { isRunning: true, status: response.status };
    } else if (response.status === 200) {
      console.log('✅ Backend is running and user is authenticated');
      return { isRunning: true, status: response.status };
    } else {
      console.log(`✅ Backend is running (status: ${response.status})`);
      return { isRunning: true, status: response.status };
    }
  } catch (error) {
    console.error('❌ Backend connectivity test failed:', error.message);
    
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      console.error(`💡 This usually means the backend server is not running on ${import.meta.env.VITE_BACKEND_URL}`);
      console.error('💡 Please start the backend server with: npm run dev (in the backend directory)');
    }
    
    return { isRunning: false, error: error.message };
  }
};

/**
 * Display backend status in console with helpful messages
 */
export const displayBackendStatus = async () => {
  console.log('🔍 Checking backend status...');
  const status = await checkBackendStatus();
  
  if (!status.isRunning) {
    console.log('\n📋 Backend Setup Instructions:');
    console.log('1. Navigate to the backend directory');
    console.log('2. Install dependencies: npm install');
    console.log('3. Set up environment variables in .env file');
    console.log('4. Initialize database: node init-db.js');
    console.log('5. Start the server: npm run dev');
    console.log(`6. Backend should be running on ${import.meta.env.VITE_BACKEND_URL}\n`);
  }
  
  return status;
};
