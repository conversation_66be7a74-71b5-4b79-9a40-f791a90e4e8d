import { useEffect, useRef } from "react";
import { X } from "lucide-react";

const Modal = ({ 
  isOpen, 
  onClose, 
  title, 
  children, 
  size = "md", // sm, md, lg, xl
  showCloseButton = true,
  closeOnOutsideClick = true,
  className = ""
}) => {
  const modalRef = useRef(null);

  // Handle escape key press
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === "Escape") {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscKey);
      document.body.style.overflow = "hidden"; // Prevent scrolling when modal is open
    }

    return () => {
      document.removeEventListener("keydown", handleEscKey);
      document.body.style.overflow = "auto"; // Restore scrolling when modal is closed
    };
  }, [isOpen, onClose]);

  // Handle outside click
  const handleOutsideClick = (e) => {
    if (closeOnOutsideClick && modalRef.current && !modalRef.current.contains(e.target)) {
      onClose();
    }
  };

  if (!isOpen) return null;

  // Determine modal width based on size
  const sizeClasses = {
    sm: "max-w-md",
    md: "max-w-lg",
    lg: "max-w-2xl",
    xl: "max-w-4xl",
    full: "max-w-full mx-4"
  };

  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/60 backdrop-blur-sm transition-opacity"
      onClick={handleOutsideClick}
    >
      <div 
        ref={modalRef}
        className={`glass-card rounded-lg shadow-xl w-full ${sizeClasses[size]} ${className} transition-all transform`}
        style={{ maxHeight: "calc(100vh - 2rem)", overflowY: "auto" }}
      >
        <div className="flex justify-between items-center p-5 border-b border-border">
          <h3 className="text-lg font-medium text-white">{title}</h3>
          {showCloseButton && (
            <button 
              onClick={onClose}
              className="p-1 rounded-full hover:bg-surface-3 transition-colors"
            >
              <X className="h-5 w-5 text-text-secondary" />
            </button>
          )}
        </div>
        <div className="p-5">
          {children}
        </div>
      </div>
    </div>
  );
};

export default Modal;
