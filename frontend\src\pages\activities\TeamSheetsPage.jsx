import { useState, useEffect, useRef } from 'react';
import {
  Users,
  Calendar,
  Search,
  Filter,
  Download,
  Clock,
  ChevronLeft,
  ChevronDown,
  ChevronUp,
  ChevronRight,
  User,
  Briefcase
} from 'lucide-react';

const TeamSheetsPage = () => {
  const [activeTab, setActiveTab] = useState('custom');
  const [searchQuery, setSearchQuery] = useState('');
  const [filterOpen, setFilterOpen] = useState(false);
  const [expandedEmployees, setExpandedEmployees] = useState({});
  const [selectedDate, setSelectedDate] = useState(formatDateForInput(new Date()));
  const [calendarOpen, setCalendarOpen] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState({
    team: 'all'
  });

  // Format date for display (e.g., "May 15, 2023")
  function formatDateForDisplay(dateString) {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-US', options);
  }

  // Format date for input value (YYYY-MM-DD)
  function formatDateForInput(date) {
    const d = new Date(date);
    const month = (d.getMonth() + 1).toString().padStart(2, '0');
    const day = d.getDate().toString().padStart(2, '0');
    const year = d.getFullYear();
    return `${year}-${month}-${day}`;
  }

  // Get yesterday's date
  function getYesterday() {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    return formatDateForInput(yesterday);
  }

  // Get today's date
  function getToday() {
    return formatDateForInput(new Date());
  }

  // Handle date change
  const handleDateChange = (date) => {
    setSelectedDate(date);
    setActiveTab('custom');
  };

  // Ref for date picker dropdown
  const datePickerRef = useRef(null);

  // Handle click outside to close date picker
  useEffect(() => {
    function handleClickOutside(event) {
      if (datePickerRef.current && !datePickerRef.current.contains(event.target)) {
        setShowDatePicker(false);
      }
    }

    // Add event listener when date picker is open
    if (showDatePicker) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    // Clean up
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showDatePicker]);

  // Mock data for employees and their hours
  const employeeData = [
    {
      id: 1,
      name: "John Doe",
      position: "Frontend Developer",
      avatar: "JD",
      totalHours: 8.5,
      date: "2023-05-15",
      projects: [
        { id: 1, name: "Website Redesign", hours: 5.5, status: "In Progress", completion: 65 },
        { id: 2, name: "Mobile App Development", hours: 3, status: "Not Started", completion: 10 }
      ]
    },
    {
      id: 2,
      name: "Sarah Williams",
      position: "UX Designer",
      avatar: "SW",
      totalHours: 7.25,
      date: "2023-05-15",
      projects: [
        { id: 1, name: "Website Redesign", hours: 4.25, status: "In Progress", completion: 65 },
        { id: 3, name: "Client Dashboard", hours: 3, status: "In Progress", completion: 40 }
      ]
    },
    {
      id: 3,
      name: "Mike Johnson",
      position: "Backend Developer",
      avatar: "MJ",
      totalHours: 9,
      date: "2023-05-15",
      projects: [
        { id: 2, name: "Mobile App Development", hours: 6, status: "Not Started", completion: 10 },
        { id: 4, name: "API Integration", hours: 3, status: "In Progress", completion: 75 }
      ]
    },
    {
      id: 4,
      name: "Jane Smith",
      position: "Project Manager",
      avatar: "JS",
      totalHours: 8,
      date: "2023-05-15",
      projects: [
        { id: 1, name: "Website Redesign", hours: 2, status: "In Progress", completion: 65 },
        { id: 2, name: "Mobile App Development", hours: 3, status: "Not Started", completion: 10 },
        { id: 3, name: "Client Dashboard", hours: 3, status: "In Progress", completion: 40 }
      ]
    },
    {
      id: 5,
      name: "Alex Kim",
      position: "DevOps Engineer",
      avatar: "AK",
      totalHours: 7.5,
      date: "2023-05-15",
      projects: [
        { id: 4, name: "API Integration", hours: 4.5, status: "In Progress", completion: 75 },
        { id: 5, name: "Database Migration", hours: 3, status: "Completed", completion: 100 }
      ]
    }
  ];

  // Yesterday's data
  const yesterdayData = [
    {
      id: 1,
      name: "John Doe",
      position: "Frontend Developer",
      avatar: "JD",
      totalHours: 7.5,
      date: "2023-05-14",
      projects: [
        { id: 1, name: "Website Redesign", hours: 4.5, status: "In Progress", completion: 60 },
        { id: 2, name: "Mobile App Development", hours: 3, status: "Not Started", completion: 5 }
      ]
    },
    {
      id: 2,
      name: "Sarah Williams",
      position: "UX Designer",
      avatar: "SW",
      totalHours: 8,
      date: "2023-05-14",
      projects: [
        { id: 1, name: "Website Redesign", hours: 5, status: "In Progress", completion: 60 },
        { id: 3, name: "Client Dashboard", hours: 3, status: "In Progress", completion: 35 }
      ]
    },
    {
      id: 3,
      name: "Mike Johnson",
      position: "Backend Developer",
      avatar: "MJ",
      totalHours: 8.5,
      date: "2023-05-14",
      projects: [
        { id: 2, name: "Mobile App Development", hours: 5.5, status: "Not Started", completion: 5 },
        { id: 4, name: "API Integration", hours: 3, status: "In Progress", completion: 70 }
      ]
    }
  ];

  // Toggle employee accordion
  const toggleEmployeeExpansion = (employeeId) => {
    setExpandedEmployees(prev => ({
      ...prev,
      [employeeId]: !prev[employeeId]
    }));
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case "Done":
      case "Completed":
        return "bg-green-500";
      case "In Progress":
        return "bg-blue-500";
      case "Not Started":
        return "bg-yellow-500";
      default:
        return "bg-text-secondary";
    }
  };

  // Additional mock data for May 13, 2023
  const pastData = [
    {
      id: 1,
      name: "John Doe",
      position: "Frontend Developer",
      avatar: "JD",
      totalHours: 6.5,
      date: "2023-05-13",
      projects: [
        { id: 1, name: "Website Redesign", hours: 3.5, status: "In Progress", completion: 55 },
        { id: 2, name: "Mobile App Development", hours: 3, status: "Not Started", completion: 0 }
      ]
    },
    {
      id: 2,
      name: "Sarah Williams",
      position: "UX Designer",
      avatar: "SW",
      totalHours: 7,
      date: "2023-05-13",
      projects: [
        { id: 1, name: "Website Redesign", hours: 4, status: "In Progress", completion: 55 },
        { id: 3, name: "Client Dashboard", hours: 3, status: "Not Started", completion: 30 }
      ]
    }
  ];

  // Mock data for May 12, 2023
  const olderData = [
    {
      id: 3,
      name: "Mike Johnson",
      position: "Backend Developer",
      avatar: "MJ",
      totalHours: 8,
      date: "2023-05-12",
      projects: [
        { id: 2, name: "Mobile App Development", hours: 5, status: "Not Started", completion: 0 },
        { id: 4, name: "API Integration", hours: 3, status: "In Progress", completion: 65 }
      ]
    },
    {
      id: 5,
      name: "Alex Kim",
      position: "DevOps Engineer",
      avatar: "AK",
      totalHours: 7,
      date: "2023-05-12",
      projects: [
        { id: 4, name: "API Integration", hours: 4, status: "In Progress", completion: 65 },
        { id: 5, name: "Database Migration", hours: 3, status: "Completed", completion: 95 }
      ]
    }
  ];

  // All data combined for date filtering
  const allData = [...employeeData, ...yesterdayData, ...pastData, ...olderData];

  // Get data for a specific date
  const getDataForDate = (dateString) => {
    return allData.filter(employee => employee.date === dateString);
  };

  // Filter employees based on active tab, date, and search query
  const getFilteredEmployees = () => {
    let dataToUse;

    if (activeTab === 'today') {
      dataToUse = employeeData;
    } else if (activeTab === 'yesterday') {
      dataToUse = yesterdayData;
    } else if (activeTab === 'custom') {
      dataToUse = getDataForDate(selectedDate);
    }

    return dataToUse.filter(employee => {
      // Filter by search query
      if (searchQuery && !employee.name.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false;
      }

      // Filter by team (would be implemented with real data)
      if (selectedFilters.team !== 'all') {
        // This is a placeholder - in a real app, you'd filter by team
        // return employee.team === selectedFilters.team;
      }

      return true;
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-semibold text-white">Team Sheets</h1>
          <p className="text-text-secondary mt-1">
            {activeTab === 'custom'
              ? `Hours for ${formatDateForDisplay(selectedDate)}`
              : activeTab === 'today'
                ? 'Hours for Today'
                : 'Hours for Yesterday'}
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-3">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-4 w-4 text-text-secondary" />
            </div>
            <input
              type="text"
              className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full pl-10 p-2.5"
              placeholder="Search employees..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <div className="relative">
            <button
              className="bg-surface-3 border border-border text-white text-sm rounded-lg px-4 py-2.5 flex items-center"
              onClick={() => setFilterOpen(!filterOpen)}
            >
              <Filter className="h-4 w-4 mr-2 text-text-secondary" />
              Filter
              {filterOpen ? (
                <ChevronUp className="h-4 w-4 ml-2 text-text-secondary" />
              ) : (
                <ChevronDown className="h-4 w-4 ml-2 text-text-secondary" />
              )}
            </button>

            {filterOpen && (
              <div className="absolute right-0 mt-2 w-64 bg-surface-2 border border-border rounded-lg shadow-lg z-10">
                <div className="p-3">
                  <h3 className="text-sm font-medium text-white mt-3 mb-2">Team</h3>
                  <div className="space-y-1">
                    <button
                      className={`w-full text-left px-2 py-1 text-sm rounded ${selectedFilters.team === 'all' ? 'bg-surface text-white' : 'text-text-secondary hover:bg-surface-3'}`}
                      onClick={() => setSelectedFilters({...selectedFilters, team: 'all'})}
                    >
                      All Teams
                    </button>
                    <button
                      className={`w-full text-left px-2 py-1 text-sm rounded ${selectedFilters.team === 'Development' ? 'bg-surface text-white' : 'text-text-secondary hover:bg-surface-3'}`}
                      onClick={() => setSelectedFilters({...selectedFilters, team: 'Development'})}
                    >
                      Development
                    </button>
                    <button
                      className={`w-full text-left px-2 py-1 text-sm rounded ${selectedFilters.team === 'Design' ? 'bg-surface text-white' : 'text-text-secondary hover:bg-surface-3'}`}
                      onClick={() => setSelectedFilters({...selectedFilters, team: 'Design'})}
                    >
                      Design
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

          <button className="bg-accent-1 text-black font-medium text-sm rounded-lg px-4 py-2.5 flex items-center">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </button>
        </div>
      </div>

      {/* Date Selection and Tabs */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4">
        <div className="flex space-x-4 items-center">
          <button
            className={`py-2 px-3 text-sm font-medium rounded-lg ${
              activeTab === 'today'
                ? 'bg-surface text-white'
                : 'text-text-secondary hover:text-white hover:bg-surface-3'
            }`}
            onClick={() => {
              setActiveTab('today');
              setSelectedDate(getToday());
            }}
          >
            Today
          </button>
          <button
            className={`py-2 px-3 text-sm font-medium rounded-lg ${
              activeTab === 'yesterday'
                ? 'bg-surface text-white'
                : 'text-text-secondary hover:text-white hover:bg-surface-3'
            }`}
            onClick={() => {
              setActiveTab('yesterday');
              setSelectedDate(getYesterday());
            }}
          >
            Yesterday
          </button>
        </div>

        {/* Date Picker */}
        <div className="flex items-center space-x-2">
          <div className="flex items-center">
            <Calendar className="h-4 w-4 text-text-secondary mr-2" />
            <span className="text-white text-sm">
              {activeTab === 'custom'
                ? formatDateForDisplay(selectedDate)
                : activeTab === 'today'
                  ? 'Today'
                  : 'Yesterday'}
            </span>
          </div>
          <div className="relative" ref={datePickerRef}>
            <button
              className="px-3 py-2 bg-surface-3 text-white text-sm rounded-lg hover:bg-surface flex items-center"
              onClick={() => setShowDatePicker(!showDatePicker)}
            >
              <span>Change Date</span>
              <ChevronDown className="h-4 w-4 ml-1 text-text-secondary" />
            </button>

            {/* Custom Date Picker Dropdown */}
            {showDatePicker && (
              <div className="absolute right-0 mt-2 p-4 w-64 bg-surface-2 border border-border rounded-lg shadow-lg z-20">
                <div className="mb-3">
                  <label className="block text-sm font-medium text-white mb-1">
                    Select Date
                  </label>
                  <input
                    type="date"
                    value={selectedDate}
                    onChange={(e) => handleDateChange(e.target.value)}
                    className="w-full bg-surface-3 border border-border text-white text-sm rounded-lg p-2.5 focus:ring-accent-1 focus:border-accent-1"
                    style={{ colorScheme: 'dark' }}
                  />
                </div>
                <div className="flex justify-between">
                  <button
                    className="px-3 py-1.5 bg-surface-3 text-white text-xs rounded hover:bg-surface"
                    onClick={() => {
                      setSelectedDate(getToday());
                      setActiveTab('today');
                      setShowDatePicker(false);
                    }}
                  >
                    Today
                  </button>
                  <button
                    className="px-3 py-1.5 bg-surface-3 text-white text-xs rounded hover:bg-surface"
                    onClick={() => {
                      setSelectedDate(getYesterday());
                      setActiveTab('yesterday');
                      setShowDatePicker(false);
                    }}
                  >
                    Yesterday
                  </button>
                  <button
                    className="px-3 py-1.5 bg-accent-1 text-black text-xs rounded hover:bg-accent-1/90"
                    onClick={() => {
                      handleDateChange(selectedDate);
                      setShowDatePicker(false);
                    }}
                  >
                    Apply
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Date Navigation */}
      <div className="flex justify-between items-center mb-6">
        <button
          className="p-2 text-text-secondary hover:text-white rounded-lg hover:bg-surface-3 flex items-center"
          onClick={() => {
            const prevDate = new Date(selectedDate);
            prevDate.setDate(prevDate.getDate() - 1);
            handleDateChange(formatDateForInput(prevDate));
          }}
        >
          <ChevronLeft className="h-5 w-5 mr-1" />
          <span className="text-sm">Previous Day</span>
        </button>

        <button
          className="p-2 text-text-secondary hover:text-white rounded-lg hover:bg-surface-3 flex items-center"
          onClick={() => {
            const nextDate = new Date(selectedDate);
            nextDate.setDate(nextDate.getDate() + 1);
            handleDateChange(formatDateForInput(nextDate));
          }}
        >
          <span className="text-sm">Next Day</span>
          <ChevronRight className="h-5 w-5 ml-1" />
        </button>
      </div>

      {/* Employee Hours List with Accordion */}
      <div className="space-y-4">
        {getFilteredEmployees().length > 0 ? (
          getFilteredEmployees().map((employee) => (
            <div key={employee.id} className="glass-card rounded-lg overflow-hidden">
              {/* Employee Header - Clickable to expand */}
              <div
                className="px-5 py-4 bg-surface-2 border-b border-border flex justify-between items-center cursor-pointer"
                onClick={() => toggleEmployeeExpansion(employee.id)}
              >
                <div className="flex items-center">
                  <div className="h-10 w-10 rounded-full bg-surface-3 flex items-center justify-center text-white text-sm font-medium mr-3">
                    {employee.avatar}
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-white">{employee.name}</h3>
                    <p className="text-sm text-text-secondary">{employee.position}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="flex items-center mr-4">
                    <Clock className="h-4 w-4 text-text-secondary mr-2" />
                    <span className="text-white font-medium">{employee.totalHours} hours</span>
                  </div>
                  {expandedEmployees[employee.id] ? (
                    <ChevronUp className="h-5 w-5 text-text-secondary" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-text-secondary" />
                  )}
                </div>
              </div>

              {/* Project Details - Expanded View */}
              {expandedEmployees[employee.id] && (
                <div className="p-5 space-y-4">
                  <h4 className="text-sm font-medium text-white flex items-center">
                    <Briefcase className="h-4 w-4 mr-2 text-text-secondary" />
                    Project Hours Breakdown
                  </h4>

                  {/* Projects List */}
                  <div className="space-y-3">
                    {employee.projects.map((project) => (
                      <div key={project.id} className="p-3 rounded-lg bg-surface-3 bg-opacity-50 hover:bg-opacity-70 transition-colors">
                        <div className="flex justify-between items-start mb-2">
                          <h4 className="font-medium text-white text-sm">
                            {project.name}
                          </h4>
                          <span
                            className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                              project.status
                            )} bg-opacity-10 text-white`}
                          >
                            {project.status}
                          </span>
                        </div>
                        <div className="flex justify-between items-center text-xs text-text-secondary mt-2">
                          <div className="flex items-center">
                            <Clock className="mr-1 w-3 h-3" />
                            <span>{project.hours} hours</span>
                          </div>
                          <div className="flex items-center">
                            <div className="w-16 bg-surface-3 rounded-full h-1.5 mr-1">
                              <div
                                className="bg-soft-blue h-1.5 rounded-full"
                                style={{ width: `${project.completion}%` }}
                              ></div>
                            </div>
                            <span>{project.completion}%</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Summary */}
                  <div className="mt-4 pt-3 border-t border-border">
                    <div className="flex justify-between text-sm">
                      <span className="text-text-secondary">Total Hours:</span>
                      <span className="text-white font-medium">{employee.totalHours} hours</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))
        ) : (
          <div className="text-center py-12 glass-card rounded-lg">
            <Calendar className="h-12 w-12 text-text-secondary mx-auto mb-3" />
            <h3 className="text-lg font-medium text-white mb-1">No Data Available</h3>
            <p className="text-text-secondary">
              {activeTab === 'custom'
                ? `No hours recorded for ${formatDateForDisplay(selectedDate)}.`
                : activeTab === 'today'
                  ? 'No hours recorded for today.'
                  : 'No hours recorded for yesterday.'}
            </p>
            <button
              className="mt-4 px-4 py-2 bg-surface-3 text-white text-sm rounded-lg hover:bg-surface"
              onClick={() => {
                setActiveTab('today');
                setSelectedDate(getToday());
              }}
            >
              View Today's Hours
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default TeamSheetsPage;
