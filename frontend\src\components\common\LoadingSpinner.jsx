import { ClipLoader } from 'react-spinners';

/**
 * A reusable loading spinner component using react-spinners ClipLoader
 * @param {Object} props Component props
 * @param {boolean} props.loading Whether the spinner is visible
 * @param {number} props.size Size of the spinner in pixels
 * @param {string} props.color Color of the spinner
 * @param {string} props.className Additional CSS classes
 * @param {boolean} props.fullScreen Whether to display as a full-screen overlay
 */
const LoadingSpinner = ({ 
  loading = true, 
  size = 35, 
  color = '#4F46E5', 
  className = '',
  fullScreen = false
}) => {
  if (fullScreen) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-background bg-opacity-75 z-50">
        <ClipLoader
          loading={loading}
          size={size}
          color={color}
          className={className}
          aria-label="Loading Spinner"
          data-testid="loader"
        />
      </div>
    );
  }

  return (
    <ClipLoader
      loading={loading}
      size={size}
      color={color}
      className={className}
      aria-label="Loading Spinner"
      data-testid="loader"
    />
  );
};

export default LoadingSpinner;
