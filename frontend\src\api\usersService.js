import axiosClient from './axiosClient';

const usersService = {
  // Get all users
  getUsers: async () => {
    try {
      const response = await axiosClient.get('/users');
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Get user by ID
  getUserById: async (id) => {
    try {
      const response = await axiosClient.get(`/users/${id}`);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Update user
  updateUser: async (id, userData) => {
    try {
      const response = await axiosClient.put(`/users/${id}`, userData);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Delete user
  deleteUser: async (id) => {
    try {
      const response = await axiosClient.delete(`/users/${id}`);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Invite new user
  inviteUser: async (inviteData) => {
    try {
      const response = await axiosClient.post('/users/invite', inviteData);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Get all users in a company
  getUsersByCompany: async (companyId) => {
    try {
      const response = await axiosClient.get(`/users/company/${companyId}`);
      return response;
    } catch (error) {
      throw error;
    }
  },
};

export default usersService;
