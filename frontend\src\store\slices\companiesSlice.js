import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { companiesService, authService } from '../../api';

// Async thunks for companies operations
export const fetchCompanies = createAsyncThunk(
  'companies/fetchCompanies',
  async (_, { rejectWithValue }) => {
    try {
      const response = await companiesService.getCompanies();
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to fetch companies'
      );
    }
  }
);

export const createCompany = createAsyncThunk(
  'companies/createCompany',
  async (companyData, { rejectWithValue }) => {
    try {
      const response = await companiesService.createCompany(companyData);
      await authService.getCurrentUser();
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to create company'
      );
    }
  }
);

export const updateCompany = createAsyncThunk(
  'companies/updateCompany',
  async ({ id, companyData }, { rejectWithValue }) => {
    try {
      const response = await companiesService.updateCompany(id, companyData);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to update company'
      );
    }
  }
);

export const deleteCompany = createAsyncThunk(
  'companies/deleteCompany',
  async (id, { rejectWithValue }) => {
    try {
      await companiesService.deleteCompany(id);
      return id;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to delete company'
      );
    }
  }
);

export const fetchCompanyUsers = createAsyncThunk(
  'companies/fetchCompanyUsers',
  async (companyId, { rejectWithValue }) => {
    try {
      const response = await companiesService.getCompanyUsers(companyId);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to fetch company users'
      );
    }
  }
);

export const inviteUserToCompany = createAsyncThunk(
  'companies/inviteUserToCompany',
  async ({ companyId, inviteData }, { rejectWithValue }) => {
    try {
      const response = await companiesService.inviteUserToCompany(companyId, inviteData);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to invite user'
      );
    }
  }
);

// Initial state
const initialState = {
  companies: [],
  currentCompany: null,
  companyUsers: [],
  loading: false,
  error: null,
  createLoading: false,
  updateLoading: false,
  deleteLoading: false,
  inviteLoading: false,
};

// Companies slice
const companiesSlice = createSlice({
  name: 'companies',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentCompany: (state, action) => {
      state.currentCompany = action.payload;
    },
    clearCurrentCompany: (state) => {
      state.currentCompany = null;
      state.companyUsers = [];
    },
  },
  extraReducers: (builder) => {
    // Fetch companies
    builder
      .addCase(fetchCompanies.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCompanies.fulfilled, (state, action) => {
        state.loading = false;
        state.companies = action.payload.companies || action.payload;
        state.error = null;
      })
      .addCase(fetchCompanies.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
    // Create company
    builder
      .addCase(createCompany.pending, (state) => {
        state.createLoading = true;
        state.error = null;
      })
      .addCase(createCompany.fulfilled, (state, action) => {
        state.createLoading = false;
        state.companies.push(action.payload.company || action.payload);
        state.error = null;
      })
      .addCase(createCompany.rejected, (state, action) => {
        state.createLoading = false;
        state.error = action.payload;
      })
      
    // Update company
    builder
      .addCase(updateCompany.pending, (state) => {
        state.updateLoading = true;
        state.error = null;
      })
      .addCase(updateCompany.fulfilled, (state, action) => {
        state.updateLoading = false;
        const updatedCompany = action.payload.company || action.payload;
        const index = state.companies.findIndex(company => company.id === updatedCompany.id);
        if (index !== -1) {
          state.companies[index] = updatedCompany;
        }
        if (state.currentCompany && state.currentCompany.id === updatedCompany.id) {
          state.currentCompany = updatedCompany;
        }
        state.error = null;
      })
      .addCase(updateCompany.rejected, (state, action) => {
        state.updateLoading = false;
        state.error = action.payload;
      })
      
    // Delete company
    builder
      .addCase(deleteCompany.pending, (state) => {
        state.deleteLoading = true;
        state.error = null;
      })
      .addCase(deleteCompany.fulfilled, (state, action) => {
        state.deleteLoading = false;
        state.companies = state.companies.filter(company => company.id !== action.payload);
        if (state.currentCompany && state.currentCompany.id === action.payload) {
          state.currentCompany = null;
          state.companyUsers = [];
        }
        state.error = null;
      })
      .addCase(deleteCompany.rejected, (state, action) => {
        state.deleteLoading = false;
        state.error = action.payload;
      })
      
    // Fetch company users
    builder
      .addCase(fetchCompanyUsers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCompanyUsers.fulfilled, (state, action) => {
        state.loading = false;
        state.companyUsers = action.payload.users || action.payload;
        state.error = null;
      })
      .addCase(fetchCompanyUsers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
    // Invite user to company
    builder
      .addCase(inviteUserToCompany.pending, (state) => {
        state.inviteLoading = true;
        state.error = null;
      })
      .addCase(inviteUserToCompany.fulfilled, (state, action) => {
        state.inviteLoading = false;
        state.error = null;
      })
      .addCase(inviteUserToCompany.rejected, (state, action) => {
        state.inviteLoading = false;
        state.error = action.payload;
      });
  },
});

export const { clearError, setCurrentCompany, clearCurrentCompany } = companiesSlice.actions;
export default companiesSlice.reducer;
