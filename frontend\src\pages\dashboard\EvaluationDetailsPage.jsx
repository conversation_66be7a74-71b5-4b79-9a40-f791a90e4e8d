import { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import {
  ArrowLeft,
  Mail,
  Calendar,
  Briefcase,
  Users,
  CheckCircle,
  XCircle,
  AlertCircle,
  Edit,
  Clock,
  FileText,
  BarChart,
  Save,
  Star,
  StarHalf,
  CheckSquare,
  Target,
  ChevronDown,
  ChevronUp,
  User,
  Building,
  BadgeCheck,
} from "lucide-react";
import SlideInModal from "../../components/common/SlideInModal";
import FilterDropdown from "../../components/common/FilterDropdown";
import DateRangePicker from "../../components/common/DateRangePicker";

// Mock employee data
const mockEmployeeDetails = {
  1: {
    id: 1,
    name: "<PERSON>",
    avatar: "/avatars/avatar-1.jpg",
    position: "Senior Developer",
    role: "Admin",
    dateJoined: "2022-01-15",
    department: "Engineering",
    email: "<EMAIL>",
    bio: "Experienced full-stack developer with 8+ years of experience in building scalable web applications.",
    employeeId: "EMP-001",
    status: "Active",
    manager: "<PERSON>",
    attendance: { present: 180, expected: 200 },
    leaves: { taken: 12, total: 24 },
    projects: [
      { id: 1, title: "Website Redesign", rating: 8 },
      { id: 2, title: "Mobile App Development", rating: 7 },
    ],
    previousEvaluations: [
      { id: 101, date: "2023-06-15", rating: 8, type: "periodic" },
      { id: 102, date: "2023-03-10", rating: 7, type: "periodic" },
    ],
  },
  2: {
    id: 2,
    name: "Jane Smith",
    avatar: "/avatars/avatar-2.jpg",
    position: "UI/UX Designer",
    role: "User",
    dateJoined: "2022-03-10",
    department: "Design",
    email: "<EMAIL>",
    bio: "Creative designer with a passion for user-centered design and accessibility.",
    employeeId: "EMP-002",
    status: "Active",
    manager: "Robert Johnson",
    attendance: { present: 190, expected: 200 },
    leaves: { taken: 8, total: 24 },
    projects: [
      { id: 1, title: "Website Redesign", rating: 9 },
      { id: 3, title: "Client Onboarding Improvement", rating: 9 },
    ],
    previousEvaluations: [
      { id: 103, date: "2023-06-15", rating: 9, type: "periodic" },
      { id: 104, date: "2023-03-10", rating: 8, type: "periodic" },
    ],
  },
};

// Mock evaluations data
const mockEvaluations = {
  1: {
    id: 1,
    employeeId: 1,
    type: "periodic",
    date: "2023-12-10",
    rating: 8,
    strengths:
      "Strong technical skills, problem-solving abilities, and teamwork. Consistently delivers high-quality code and meets deadlines.",
    improvements:
      "Could improve documentation practices and communication with non-technical team members.",
    feedback:
      "John has been a valuable asset to the team this quarter. His technical expertise and problem-solving skills have helped us overcome several challenges in the Website Redesign project. He consistently delivers high-quality code and meets deadlines. However, he could improve his documentation practices and communication with non-technical team members.",
    status: "In Progress",
    createdBy: "Sarah Brown",
    createdAt: "2023-12-01",
    completedAt: null,
  },
  2: {
    id: 2,
    employeeId: 2,
    type: "periodic",
    date: "2023-12-10",
    rating: 9,
    strengths:
      "Exceptional design skills, attention to detail, and user-centered approach. Consistently delivers high-quality designs that receive positive feedback from clients.",
    improvements: "Could improve time management and prioritization of tasks.",
    feedback:
      "Jane has exceeded expectations this quarter. Her design work on the Website Redesign project has received excellent feedback from clients. She consistently delivers high-quality designs that are both visually appealing and user-friendly. Her attention to detail and user-centered approach have been particularly valuable. However, she could improve her time management and prioritization of tasks to avoid occasional last-minute rushes.",
    status: "Completed",
    createdBy: "Robert Johnson",
    createdAt: "2023-12-01",
    completedAt: "2023-12-10",
  },
};

const EvaluationDetailsPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  // Only two tabs now: overview (evaluation details) and history
  const [activeTab, setActiveTab] = useState("overview");
  const [isEditing, setIsEditing] = useState(false);
  const [evaluation, setEvaluation] = useState(null);
  const [employee, setEmployee] = useState(null);
  const [editedEvaluation, setEditedEvaluation] = useState(null);

  // State for performance metrics modal
  const [showMetricsModal, setShowMetricsModal] = useState(false);

  // State for performance period
  const [metricsPeriod, setMetricsPeriod] = useState("last-evaluation");

  // Fetch evaluation and employee data
  useEffect(() => {
    const evaluationData = mockEvaluations[id];
    if (evaluationData) {
      setEvaluation(evaluationData);
      setEditedEvaluation({ ...evaluationData });

      // Get employee details
      const employeeData = mockEmployeeDetails[evaluationData.employeeId];
      if (employeeData) {
        setEmployee(employeeData);
      }
    }
  }, [id]);

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setEditedEvaluation({
      ...editedEvaluation,
      [name]: value,
    });
  };

  // Handle rating change (1-10 scale with decimal precision)
  const handleRatingChange = (rating) => {
    // Allow for decimal precision by providing a way to set half-star ratings
    setEditedEvaluation({
      ...editedEvaluation,
      rating,
    });
  };

  // Handle decimal rating change (for fine-tuning the rating)
  const handleDecimalRatingChange = (e) => {
    const value = parseFloat(e.target.value);
    if (!isNaN(value) && value >= 0 && value <= 10) {
      setEditedEvaluation({
        ...editedEvaluation,
        rating: value,
      });
    }
  };

  // Handle save
  const handleSave = () => {
    // In a real app, this would update the evaluation data in the backend
    setEvaluation({
      ...editedEvaluation,
      status:
        editedEvaluation.status === "In Progress"
          ? "Completed"
          : editedEvaluation.status,
      completedAt:
        editedEvaluation.status === "In Progress"
          ? new Date().toISOString()
          : editedEvaluation.completedAt,
    });
    setIsEditing(false);
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // Render rating (1-10 scale) with stars and decimal precision
  const renderRating = (rating, editable = false) => {
    // Convert rating to a scale of 5 stars (since we're using 10 as max)
    const maxStars = 5;
    const scaledRating = (rating || 0) / 2; // Convert 10-scale to 5-scale

    return (
      <div className="flex flex-wrap items-center gap-1">
        <div className="flex">
          {[...Array(maxStars)].map((_, index) => {
            const starValue = index + 1;
            const isFullStar = starValue <= Math.floor(scaledRating);
            const isHalfStar =
              !isFullStar &&
              starValue <= Math.ceil(scaledRating) &&
              starValue - 0.5 <= scaledRating;

            return (
              <button
                key={index}
                type="button"
                onClick={
                  editable ? () => handleRatingChange(starValue * 2) : undefined
                }
                className={`p-1 focus:outline-none ${
                  !editable ? "cursor-default" : "cursor-pointer"
                }`}
                disabled={!editable}
                title={`${starValue * 2} out of 10`}
              >
                {isFullStar ? (
                  <Star className="h-6 w-6 text-yellow-400 fill-yellow-400" />
                ) : isHalfStar ? (
                  <StarHalf className="h-6 w-6 text-yellow-400 fill-yellow-400" />
                ) : (
                  <Star className="h-6 w-6 text-gray-500" />
                )}
              </button>
            );
          })}
        </div>
        <span className="ml-2 text-white font-medium">
          {rating ? rating.toFixed(1) : "0.0"}/10
        </span>
      </div>
    );
  };

  // If evaluation not found
  if (!evaluation || !employee) {
    return (
      <div className="glass-card p-8 rounded-lg text-center">
        <AlertCircle className="h-12 w-12 mx-auto text-accent-4 mb-4" />
        <h3 className="text-lg font-medium text-white mb-2">
          Evaluation Not Found
        </h3>
        <p className="text-text-secondary max-w-md mx-auto mb-6">
          The evaluation you are looking for does not exist or has been removed.
        </p>
        <button
          onClick={() => navigate("/dashboard/performance-evaluations")}
          className="px-4 py-2 btn-white font-medium rounded-lg text-sm"
        >
          Back to Evaluations
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page header with back button, comprehensive employee details, and action buttons */}
      <div className="glass-card p-6 rounded-lg">
        <div className="flex items-start mb-6">
          <button
            onClick={() => navigate("/dashboard/performance-evaluations")}
            className="p-2 mr-4 btn-white-ghost rounded-full"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div>
            <h2 className="text-2xl font-bold text-white">
              {evaluation.type === "periodic"
                ? "Periodic Evaluation"
                : "Project Review"}
            </h2>
            <p className="text-sm text-text-secondary">
              {formatDate(evaluation.date)}
            </p>
          </div>
        </div>

        {/* Comprehensive Employee Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          {/* Employee basic info */}
          <div className="flex items-start">
            <div className="w-16 h-16 rounded-full bg-surface-3 overflow-hidden mr-4">
              {employee.avatar ? (
                <img
                  src={employee.avatar}
                  alt={employee.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.target.onerror = null;
                    e.target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(
                      employee.name
                    )}&background=2A2D3E&color=fff&size=128`;
                  }}
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-surface-3 text-white text-2xl font-bold">
                  {employee.name
                    .split(" ")
                    .map((n) => n[0])
                    .join("")}
                </div>
              )}
            </div>
            <div>
              <h3 className="text-xl font-medium text-white">
                {employee.name}
              </h3>
              <div className="flex items-center mt-1">
                <div
                  className={`px-2 py-0.5 text-xs rounded-full mr-2 ${
                    employee.status === "Active"
                      ? "bg-green-900/30 text-green-300"
                      : "bg-red-900/30 text-red-300"
                  }`}
                >
                  {employee.status}
                </div>
                <p className="text-sm text-text-secondary">
                  {employee.employeeId}
                </p>
              </div>
              <p className="text-sm text-text-secondary mt-1">
                Joined {formatDate(employee.dateJoined)}
              </p>
            </div>
          </div>

          {/* Employee Information */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-white mb-2">
              Employee Information
            </h4>
            <div className="flex items-center">
              <Mail className="h-4 w-4 text-text-secondary mr-2" />
              <p className="text-sm text-white">{employee.email}</p>
            </div>
            <div className="flex items-center">
              <Briefcase className="h-4 w-4 text-text-secondary mr-2" />
              <p className="text-sm text-white">{employee.position}</p>
            </div>
            <div className="flex items-center">
              <Building className="h-4 w-4 text-text-secondary mr-2" />
              <p className="text-sm text-white">{employee.department}</p>
            </div>
          </div>

          {/* Role & Management */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-white mb-2">
              Role & Management
            </h4>
            <div className="flex items-center">
              <BadgeCheck className="h-4 w-4 text-text-secondary mr-2" />
              <p className="text-sm text-white">{employee.role}</p>
            </div>
            <div className="flex items-center">
              <User className="h-4 w-4 text-text-secondary mr-2" />
              <p className="text-sm text-white">{employee.manager}</p>
            </div>
            <div className="flex items-center">
              <Star className="h-4 w-4 text-text-secondary mr-2" />
              <p className="text-sm text-white">
                {employee.previousEvaluations &&
                employee.previousEvaluations.length > 0
                  ? `${(
                      employee.previousEvaluations.reduce(
                        (sum, evalItem) => sum + evalItem.rating,
                        0
                      ) / employee.previousEvaluations.length
                    ).toFixed(1)}/10 avg. rating`
                  : "No previous ratings"}
              </p>
            </div>
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex flex-wrap gap-2 justify-end">
          <button
            className="px-4 py-2 btn-white-ghost font-medium rounded-lg text-sm flex items-center"
            onClick={() => setShowMetricsModal(true)}
          >
            <BarChart className="h-4 w-4 mr-2" />
            Performance Metrics
          </button>

          {!isEditing ? (
            <button
              className="px-4 py-2 btn-white font-medium rounded-lg text-sm flex items-center"
              onClick={() => setIsEditing(true)}
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit Evaluation
            </button>
          ) : (
            <>
              <button
                className="px-4 py-2 btn-white-ghost font-medium rounded-lg text-sm"
                onClick={() => {
                  setIsEditing(false);
                  setEditedEvaluation({ ...evaluation });
                }}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 btn-white font-medium rounded-lg text-sm flex items-center"
                onClick={handleSave}
              >
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </button>
            </>
          )}
        </div>
      </div>

      {/* Status badge */}
      <div className="flex items-center">
        <span
          className={`px-3 py-1 text-sm rounded-full ${
            evaluation.status === "Completed"
              ? "bg-green-900/30 text-green-300"
              : "bg-blue-900/30 text-blue-300"
          }`}
        >
          {evaluation.status}
        </span>
        {evaluation.status === "Completed" && (
          <span className="ml-2 text-sm text-text-secondary">
            Completed on {formatDate(evaluation.completedAt)}
          </span>
        )}
      </div>

      {/* Tabs - Simplified with only Evaluation and History tabs */}
      <div className="border-b border-border">
        <div className="flex overflow-x-auto hide-scrollbar">
          <nav className="flex space-x-1" aria-label="Evaluation Tabs">
            <button
              onClick={() => setActiveTab("overview")}
              className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                activeTab === "overview"
                  ? "border-accent-1 text-white"
                  : "border-transparent text-text-secondary hover:text-white hover:border-border"
              }`}
            >
              <FileText className="h-4 w-4 inline mr-2" />
              Evaluation Details
            </button>

            <button
              onClick={() => setActiveTab("history")}
              className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                activeTab === "history"
                  ? "border-accent-1 text-white"
                  : "border-transparent text-text-secondary hover:text-white hover:border-border"
              }`}
            >
              <Calendar className="h-4 w-4 inline mr-2" />
              Evaluation History
            </button>
          </nav>
        </div>
      </div>

      {/* Tab content */}
      <div className="glass-card rounded-lg overflow-hidden">
        {/* Evaluation Tab */}
        {activeTab === "overview" && (
          <div className="p-6">
            {isEditing ? (
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-white mb-2">
                    Performance Rating (1-10)
                  </label>
                  <div className="flex flex-col space-y-3">
                    {renderRating(editedEvaluation.rating, true)}
                    <div className="flex items-center mt-2">
                      <label className="text-xs text-text-secondary mr-2">
                        Fine-tune rating:
                      </label>
                      <input
                        type="number"
                        min="0"
                        max="10"
                        step="0.1"
                        value={editedEvaluation.rating}
                        onChange={handleDecimalRatingChange}
                        className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 w-20 p-1.5"
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <label
                    htmlFor="strengths"
                    className="block text-sm font-medium text-white mb-2"
                  >
                    Strengths
                  </label>
                  <textarea
                    id="strengths"
                    name="strengths"
                    value={editedEvaluation.strengths}
                    onChange={handleInputChange}
                    rows="3"
                    className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
                    placeholder="List key strengths demonstrated..."
                  ></textarea>
                </div>

                <div>
                  <label
                    htmlFor="improvements"
                    className="block text-sm font-medium text-white mb-2"
                  >
                    Areas for Improvement
                  </label>
                  <textarea
                    id="improvements"
                    name="improvements"
                    value={editedEvaluation.improvements}
                    onChange={handleInputChange}
                    rows="3"
                    className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
                    placeholder="List areas that need improvement..."
                  ></textarea>
                </div>

                <div>
                  <label
                    htmlFor="feedback"
                    className="block text-sm font-medium text-white mb-2"
                  >
                    Overall Feedback
                  </label>
                  <textarea
                    id="feedback"
                    name="feedback"
                    value={editedEvaluation.feedback}
                    onChange={handleInputChange}
                    rows="5"
                    className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
                    placeholder="Provide detailed feedback on performance..."
                  ></textarea>
                </div>

                <div>
                  <label
                    htmlFor="status"
                    className="block text-sm font-medium text-white mb-2"
                  >
                    Status
                  </label>
                  <select
                    id="status"
                    name="status"
                    value={editedEvaluation.status}
                    onChange={handleInputChange}
                    className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
                  >
                    <option value="In Progress">In Progress</option>
                    <option value="Completed">Completed</option>
                  </select>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                <div>
                  <h3 className="text-md font-medium text-white mb-3">
                    Performance Rating
                  </h3>
                  {renderRating(evaluation.rating)}
                </div>

                <div>
                  <h3 className="text-md font-medium text-white mb-3">
                    Strengths
                  </h3>
                  <p className="text-sm text-text-secondary bg-surface-3 p-4 rounded-lg">
                    {evaluation.strengths}
                  </p>
                </div>

                <div>
                  <h3 className="text-md font-medium text-white mb-3">
                    Areas for Improvement
                  </h3>
                  <p className="text-sm text-text-secondary bg-surface-3 p-4 rounded-lg">
                    {evaluation.improvements}
                  </p>
                </div>

                <div>
                  <h3 className="text-md font-medium text-white mb-3">
                    Overall Feedback
                  </h3>
                  <p className="text-sm text-text-secondary bg-surface-3 p-4 rounded-lg">
                    {evaluation.feedback}
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-white mb-1">
                      Created By
                    </h3>
                    <p className="text-sm text-text-secondary">
                      {evaluation.createdBy}
                    </p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-white mb-1">
                      Created On
                    </h3>
                    <p className="text-sm text-text-secondary">
                      {formatDate(evaluation.createdAt)}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Employee Overview Tab */}
        {activeTab === "employee" && (
          <div className="p-6">
            <div className="flex flex-col md:flex-row gap-6">
              {/* Avatar and basic info */}
              <div className="flex flex-col items-center md:items-start">
                <div className="w-32 h-32 rounded-full bg-surface-3 mb-4 overflow-hidden">
                  {employee.avatar ? (
                    <img
                      src={employee.avatar}
                      alt={employee.name}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.target.onerror = null;
                        e.target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(
                          employee.name
                        )}&background=2A2D3E&color=fff&size=128`;
                      }}
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-surface-3 text-white text-4xl font-bold">
                      {employee.name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </div>
                  )}
                </div>
                <div
                  className={`px-3 py-1 text-xs rounded-full mb-2 ${
                    employee.status === "Active"
                      ? "bg-green-900/30 text-green-300"
                      : "bg-red-900/30 text-red-300"
                  }`}
                >
                  {employee.status}
                </div>
                <div className="text-sm text-text-secondary mb-1">
                  <span className="font-medium text-white">
                    {employee.employeeId}
                  </span>
                </div>
                <div className="text-sm text-text-secondary">
                  Joined {formatDate(employee.dateJoined)}
                </div>
              </div>

              {/* Employee details */}
              <div className="flex-1">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-md font-medium text-white mb-4">
                      Employee Information
                    </h3>
                    <div className="space-y-3">
                      <div className="flex items-start">
                        <Mail className="h-5 w-5 text-text-secondary mr-3 mt-0.5" />
                        <div>
                          <p className="text-sm text-white">{employee.email}</p>
                          <p className="text-xs text-text-secondary">Email</p>
                        </div>
                      </div>
                      <div className="flex items-start">
                        <Briefcase className="h-5 w-5 text-text-secondary mr-3 mt-0.5" />
                        <div>
                          <p className="text-sm text-white">
                            {employee.position}
                          </p>
                          <p className="text-xs text-text-secondary">
                            Position
                          </p>
                        </div>
                      </div>
                      <div className="flex items-start">
                        <Users className="h-5 w-5 text-text-secondary mr-3 mt-0.5" />
                        <div>
                          <p className="text-sm text-white">
                            {employee.department}
                          </p>
                          <p className="text-xs text-text-secondary">
                            Department
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-md font-medium text-white mb-4">
                      Role & Management
                    </h3>
                    <div className="space-y-3">
                      <div className="flex items-start">
                        <Users className="h-5 w-5 text-text-secondary mr-3 mt-0.5" />
                        <div>
                          <p className="text-sm text-white">
                            {employee.manager}
                          </p>
                          <p className="text-xs text-text-secondary">Manager</p>
                        </div>
                      </div>
                      <div className="flex items-start">
                        <Briefcase className="h-5 w-5 text-text-secondary mr-3 mt-0.5" />
                        <div>
                          <p className="text-sm text-white">{employee.role}</p>
                          <p className="text-xs text-text-secondary">Role</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-6">
                  <h3 className="text-md font-medium text-white mb-2">Bio</h3>
                  <p className="text-sm text-text-secondary">{employee.bio}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Performance Metrics Tab */}
        {activeTab === "performance" && (
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Attendance Stats */}
              <div>
                <h3 className="text-md font-medium text-white mb-4">
                  Attendance Statistics
                </h3>
                <div className="glass-card p-4 rounded-lg bg-surface-2 mb-6">
                  <div className="mb-4">
                    <p className="text-sm text-text-secondary mb-1">
                      Present Days
                    </p>
                    <div className="flex items-center">
                      <p className="text-xl font-bold text-white">
                        {employee.attendance.present}
                      </p>
                      <p className="text-sm text-text-secondary ml-2">
                        / {employee.attendance.expected} expected
                      </p>
                    </div>
                    <div className="w-full bg-surface-3 rounded-full h-2 mt-2">
                      <div
                        className="bg-soft-blue h-2 rounded-full"
                        style={{
                          width: `${
                            (employee.attendance.present /
                              employee.attendance.expected) *
                            100
                          }%`,
                        }}
                      ></div>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm text-text-secondary mb-1">
                      Leave Usage
                    </p>
                    <div className="flex items-center">
                      <p className="text-xl font-bold text-white">
                        {employee.leaves.taken}
                      </p>
                      <p className="text-sm text-text-secondary ml-2">
                        / {employee.leaves.total} available
                      </p>
                    </div>
                    <div className="w-full bg-surface-3 rounded-full h-2 mt-2">
                      <div
                        className="bg-soft-blue h-2 rounded-full"
                        style={{
                          width: `${
                            (employee.leaves.taken / employee.leaves.total) *
                            100
                          }%`,
                        }}
                      ></div>
                    </div>
                  </div>
                </div>

                <h3 className="text-md font-medium text-white mb-4">
                  Projects Completed
                </h3>
                <div className="space-y-4">
                  {employee.projects.map((project) => (
                    <div
                      key={project.id}
                      className="glass-card p-4 rounded-lg bg-surface-2"
                    >
                      <div className="flex justify-between items-center mb-2">
                        <h4 className="text-sm font-medium text-white">
                          {project.title}
                        </h4>
                        <div className="flex items-center">
                          <div className="flex mr-1">
                            {[...Array(5)].map((_, index) => {
                              const starValue = (index + 1) * 2;
                              const projectRating = project.rating;

                              // For a 10-point scale converted to 5 stars
                              const isFullStar =
                                starValue <= Math.floor(projectRating);
                              const isHalfStar =
                                !isFullStar &&
                                starValue - 1 <= Math.floor(projectRating);

                              return isFullStar ? (
                                <Star
                                  key={index}
                                  className="h-3 w-3 text-yellow-400 fill-yellow-400"
                                />
                              ) : isHalfStar ? (
                                <StarHalf
                                  key={index}
                                  className="h-3 w-3 text-yellow-400 fill-yellow-400"
                                />
                              ) : (
                                <Star
                                  key={index}
                                  className="h-3 w-3 text-gray-500"
                                />
                              );
                            })}
                          </div>
                          <span className="text-xs text-text-secondary">
                            {project.rating.toFixed(1)}/10
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Performance Trend */}
              <div>
                <h3 className="text-md font-medium text-white mb-4">
                  Performance Trend
                </h3>
                <div className="glass-card p-4 rounded-lg bg-surface-2 mb-6">
                  <div className="h-48 flex items-end justify-between px-2">
                    {employee.previousEvaluations.map((evalItem, index) => (
                      <div
                        key={evalItem.id}
                        className="flex flex-col items-center"
                      >
                        <div
                          className="w-8 bg-soft-blue rounded-t-md"
                          style={{ height: `${evalItem.rating * 4}px` }}
                        ></div>
                        <p className="text-xs text-text-secondary mt-2">
                          {formatDate(evalItem.date).split(" ")[0]}
                        </p>
                      </div>
                    ))}
                    <div className="flex flex-col items-center">
                      <div
                        className="w-8 bg-accent-1 rounded-t-md"
                        style={{ height: `${evaluation.rating * 4}px` }}
                      ></div>
                      <p className="text-xs text-text-secondary mt-2">
                        Current
                      </p>
                    </div>
                  </div>
                </div>

                <h3 className="text-md font-medium text-white mb-4">
                  Performance Summary
                </h3>
                <div className="glass-card p-4 rounded-lg bg-surface-2">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-text-secondary mb-1">
                        Current Rating
                      </p>
                      <p className="text-xl font-bold text-white">
                        {evaluation.rating}/10
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-text-secondary mb-1">
                        Average Rating
                      </p>
                      <p className="text-xl font-bold text-white">
                        {(
                          employee.previousEvaluations.reduce(
                            (sum, evalItem) => sum + evalItem.rating,
                            0
                          ) / employee.previousEvaluations.length
                        ).toFixed(1)}
                        /10
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-text-secondary mb-1">
                        Projects
                      </p>
                      <p className="text-xl font-bold text-white">
                        {employee.projects.length}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-text-secondary mb-1">
                        Evaluations
                      </p>
                      <p className="text-xl font-bold text-white">
                        {employee.previousEvaluations.length + 1}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Evaluation History Tab */}
        {activeTab === "history" && (
          <div className="p-6">
            <h3 className="text-md font-medium text-white mb-4">
              Previous Evaluations
            </h3>
            {employee.previousEvaluations.length > 0 ? (
              <div className="space-y-4">
                {employee.previousEvaluations.map((prevEval) => (
                  <div
                    key={prevEval.id}
                    className="glass-card p-4 rounded-lg bg-surface-2 hover:border hover:border-accent-1 cursor-pointer"
                    onClick={() =>
                      navigate(`/dashboard/evaluations/${prevEval.id}`)
                    }
                  >
                    <div className="flex justify-between items-center mb-2">
                      <div className="text-white font-medium">
                        {prevEval.type === "periodic"
                          ? "Periodic Review"
                          : "Project Review"}
                      </div>
                      <div className="text-text-secondary text-sm">
                        {formatDate(prevEval.date)}
                      </div>
                    </div>
                    <div className="flex items-center">
                      <div className="text-text-secondary text-sm mr-2">
                        Rating:
                      </div>
                      <div className="flex items-center">
                        <div className="flex mr-2">
                          {[...Array(5)].map((_, index) => {
                            const starValue = (index + 1) * 2;
                            const evalRating = prevEval.rating;

                            // For a 10-point scale converted to 5 stars
                            const isFullStar =
                              starValue <= Math.floor(evalRating);
                            const isHalfStar =
                              !isFullStar &&
                              starValue - 1 <= Math.floor(evalRating);

                            return isFullStar ? (
                              <Star
                                key={index}
                                className="h-3 w-3 text-yellow-400 fill-yellow-400"
                              />
                            ) : isHalfStar ? (
                              <StarHalf
                                key={index}
                                className="h-3 w-3 text-yellow-400 fill-yellow-400"
                              />
                            ) : (
                              <Star
                                key={index}
                                className="h-3 w-3 text-gray-500"
                              />
                            );
                          })}
                        </div>
                        <div className="text-white text-sm">
                          {prevEval.rating.toFixed(1)}/10
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-text-secondary">
                No previous evaluations found.
              </p>
            )}
          </div>
        )}
      </div>

      {/* Performance Metrics Slide-in Modal */}
      <SlideInModal
        isOpen={showMetricsModal}
        onClose={() => setShowMetricsModal(false)}
        title="Performance Metrics"
        width="600px"
      >
        <div className="space-y-6">
          {/* Period selection */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-white mb-2">
              Performance Period
            </label>
            <div className="flex space-x-2">
              <button
                className={`px-3 py-2 text-sm rounded-lg ${
                  metricsPeriod === "last-evaluation"
                    ? "bg-surface-2 text-white"
                    : "bg-surface-3 text-text-secondary hover:text-white"
                }`}
                onClick={() => setMetricsPeriod("last-evaluation")}
              >
                Since Last Evaluation
              </button>
              <button
                className={`px-3 py-2 text-sm rounded-lg ${
                  metricsPeriod === "date-joined"
                    ? "bg-surface-2 text-white"
                    : "bg-surface-3 text-text-secondary hover:text-white"
                }`}
                onClick={() => setMetricsPeriod("date-joined")}
              >
                Since Date Joined
              </button>
              <button
                className={`px-3 py-2 text-sm rounded-lg ${
                  metricsPeriod === "custom"
                    ? "bg-surface-2 text-white"
                    : "bg-surface-3 text-text-secondary hover:text-white"
                }`}
                onClick={() => setMetricsPeriod("custom")}
              >
                Custom Range
              </button>
            </div>

            {metricsPeriod === "custom" && (
              <div className="mt-3">
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-xs text-text-secondary mb-1">
                      Start Date
                    </label>
                    <input
                      type="date"
                      className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-text-secondary mb-1">
                      End Date
                    </label>
                    <input
                      type="date"
                      className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Performance metrics */}
          <div className="space-y-6">
            {/* Overall Rating */}
            <div className="glass-card p-4 rounded-lg bg-surface-2">
              <h3 className="text-md font-medium text-white mb-3">
                Overall Rating
              </h3>
              <div className="flex items-center justify-center">
                <div className="w-32 h-32 rounded-full bg-surface-3 border-4 border-soft-blue flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-white">
                      {employee.previousEvaluations &&
                      employee.previousEvaluations.length > 0
                        ? (
                            employee.previousEvaluations.reduce(
                              (sum, evalItem) => sum + evalItem.rating,
                              0
                            ) / employee.previousEvaluations.length
                          ).toFixed(1)
                        : evaluation.rating.toFixed(1)}
                    </div>
                    <div className="flex justify-center mt-1">
                      {[...Array(5)].map((_, index) => {
                        const avgRating =
                          employee.previousEvaluations &&
                          employee.previousEvaluations.length > 0
                            ? employee.previousEvaluations.reduce(
                                (sum, evalItem) => sum + evalItem.rating,
                                0
                              ) /
                              employee.previousEvaluations.length /
                              2
                            : evaluation.rating / 2;

                        const starValue = index + 1;
                        const isFullStar = starValue <= Math.floor(avgRating);
                        const isHalfStar =
                          !isFullStar &&
                          starValue <= Math.ceil(avgRating) &&
                          starValue - 0.5 <= avgRating;

                        return isFullStar ? (
                          <Star
                            key={index}
                            className="h-4 w-4 text-yellow-400 fill-yellow-400"
                          />
                        ) : isHalfStar ? (
                          <StarHalf
                            key={index}
                            className="h-4 w-4 text-yellow-400 fill-yellow-400"
                          />
                        ) : (
                          <Star key={index} className="h-4 w-4 text-gray-500" />
                        );
                      })}
                    </div>
                    <div className="text-xs text-text-secondary mt-1">
                      out of 10
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Performance Trend */}
            <div className="glass-card p-4 rounded-lg bg-surface-2">
              <h3 className="text-md font-medium text-white mb-3">
                Performance Trend
              </h3>
              <div className="flex justify-between items-end h-40 px-2">
                {employee.previousEvaluations.map((evalItem, index) => (
                  <div key={evalItem.id} className="flex flex-col items-center">
                    <div
                      className="w-8 bg-soft-blue rounded-t-md"
                      style={{ height: `${evalItem.rating * 8}px` }}
                    ></div>
                    <p className="text-xs text-text-secondary mt-2">
                      {formatDate(evalItem.date).split(" ")[0]}
                    </p>
                  </div>
                ))}
              </div>
            </div>

            {/* Key Metrics */}
            <div className="glass-card p-4 rounded-lg bg-surface-2">
              <h3 className="text-md font-medium text-white mb-3">
                Key Metrics
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-surface-3 p-3 rounded-lg">
                  <div className="text-sm text-text-secondary mb-1">
                    Completed Projects
                  </div>
                  <div className="text-xl font-medium text-white">
                    {employee.projects ? employee.projects.length : 0}
                  </div>
                </div>
                <div className="bg-surface-3 p-3 rounded-lg">
                  <div className="text-sm text-text-secondary mb-1">
                    Tasks Completed
                  </div>
                  <div className="text-xl font-medium text-white">
                    {employee.projects ? employee.projects.length * 3 : 0}
                  </div>
                </div>
                <div className="bg-surface-3 p-3 rounded-lg">
                  <div className="text-sm text-text-secondary mb-1">
                    On-Time Completion
                  </div>
                  <div className="text-xl font-medium text-white">
                    {Math.round(Math.random() * 20 + 80)}%
                  </div>
                </div>
                <div className="bg-surface-3 p-3 rounded-lg">
                  <div className="text-sm text-text-secondary mb-1">
                    Attendance Rate
                  </div>
                  <div className="text-xl font-medium text-white">
                    {employee.attendance
                      ? Math.round(
                          (employee.attendance.present /
                            employee.attendance.expected) *
                            100
                        )
                      : 95}
                    %
                  </div>
                </div>
              </div>
            </div>

            {/* Skill Assessment */}
            <div className="glass-card p-4 rounded-lg bg-surface-2">
              <h3 className="text-md font-medium text-white mb-3">
                Skill Assessment
              </h3>
              <div className="space-y-3">
                {/* Generate mock skills based on employee position */}
                {(() => {
                  // Mock skills based on position
                  const mockSkills = [];
                  if (employee.position.toLowerCase().includes("developer")) {
                    mockSkills.push(
                      {
                        name: "JavaScript",
                        level: Math.floor(Math.random() * 3) + 7,
                      },
                      {
                        name: "React",
                        level: Math.floor(Math.random() * 3) + 7,
                      },
                      {
                        name: "Node.js",
                        level: Math.floor(Math.random() * 3) + 6,
                      },
                      {
                        name: "Problem Solving",
                        level: Math.floor(Math.random() * 2) + 8,
                      }
                    );
                  } else if (
                    employee.position.toLowerCase().includes("design")
                  ) {
                    mockSkills.push(
                      {
                        name: "UI Design",
                        level: Math.floor(Math.random() * 2) + 8,
                      },
                      {
                        name: "UX Research",
                        level: Math.floor(Math.random() * 3) + 7,
                      },
                      {
                        name: "Figma",
                        level: Math.floor(Math.random() * 2) + 8,
                      },
                      {
                        name: "Visual Communication",
                        level: Math.floor(Math.random() * 2) + 8,
                      }
                    );
                  } else {
                    mockSkills.push(
                      {
                        name: "Communication",
                        level: Math.floor(Math.random() * 2) + 8,
                      },
                      {
                        name: "Leadership",
                        level: Math.floor(Math.random() * 3) + 7,
                      },
                      {
                        name: "Project Management",
                        level: Math.floor(Math.random() * 3) + 7,
                      },
                      {
                        name: "Problem Solving",
                        level: Math.floor(Math.random() * 2) + 8,
                      }
                    );
                  }

                  return mockSkills.map((skill) => (
                    <div key={skill.name} className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span className="text-white">{skill.name}</span>
                        <span className="text-text-secondary">
                          {skill.level}/10
                        </span>
                      </div>
                      <div className="w-full bg-surface-3 rounded-full h-2">
                        <div
                          className="bg-soft-blue h-2 rounded-full"
                          style={{ width: `${(skill.level / 10) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  ));
                })()}
              </div>
            </div>
          </div>
        </div>
      </SlideInModal>
    </div>
  );
};

export default EvaluationDetailsPage;
