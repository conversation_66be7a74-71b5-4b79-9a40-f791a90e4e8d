import { useState, useEffect } from 'react';
import { 
  companiesService, 
  usersService, 
  projectsService, 
  tasksService, 
  attendanceService, 
  leavesService, 
  evaluationsService 
} from '../api';
import LoadingSpinner from '../components/common/LoadingSpinner';

/**
 * This component demonstrates how to use the various API services
 * It's meant as a reference for developers implementing features
 */
const ApiUsageExamples = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);

  // Example: Fetch companies
  const fetchCompanies = async () => {
    setLoading(true);
    setError(null);
    try {
      const companies = await companiesService.getCompanies();
      setData(companies);
      console.log('Companies:', companies);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to fetch companies');
    } finally {
      setLoading(false);
    }
  };

  // Example: Create a new company
  const createCompany = async () => {
    setLoading(true);
    setError(null);
    try {
      const newCompany = await companiesService.createCompany({
        name: "Example Corp",
        description: "A sample company",
        checkInHoursStart: "09:00",
        checkInHoursEnd: "17:00",
        messageFormat: "Daily check-in: What are you working on today?"
      });
      setData(newCompany);
      console.log('New company created:', newCompany);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to create company');
    } finally {
      setLoading(false);
    }
  };

  // Example: Fetch users
  const fetchUsers = async () => {
    setLoading(true);
    setError(null);
    try {
      const users = await usersService.getUsers();
      setData(users);
      console.log('Users:', users);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to fetch users');
    } finally {
      setLoading(false);
    }
  };

  // Example: Fetch projects
  const fetchProjects = async () => {
    setLoading(true);
    setError(null);
    try {
      const projects = await projectsService.getProjects();
      setData(projects);
      console.log('Projects:', projects);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to fetch projects');
    } finally {
      setLoading(false);
    }
  };

  // Example: Create a new project
  const createProject = async () => {
    setLoading(true);
    setError(null);
    try {
      const newProject = await projectsService.createProject({
        title: "Sample Project",
        description: "A sample project for testing",
        companyId: "your-company-id-here", // Replace with actual company ID
        dueDate: "2024-12-31T23:59:59Z",
        projectFormat: "statuses"
      });
      setData(newProject);
      console.log('New project created:', newProject);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to create project');
    } finally {
      setLoading(false);
    }
  };

  // Example: Check in attendance
  const checkIn = async () => {
    setLoading(true);
    setError(null);
    try {
      const checkInResult = await attendanceService.checkIn({
        companyId: "your-company-id-here", // Replace with actual company ID
        checkInMessage: "Starting work on the new feature"
      });
      setData(checkInResult);
      console.log('Check-in successful:', checkInResult);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to check in');
    } finally {
      setLoading(false);
    }
  };

  // Example: Create leave request
  const createLeaveRequest = async () => {
    setLoading(true);
    setError(null);
    try {
      const leaveRequest = await leavesService.createLeaveRequest({
        companyId: "your-company-id-here", // Replace with actual company ID
        startDate: "2024-12-24",
        endDate: "2024-12-31",
        reason: "Holiday vacation"
      });
      setData(leaveRequest);
      console.log('Leave request created:', leaveRequest);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to create leave request');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">API Usage Examples</h1>
      
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
        <button 
          onClick={fetchCompanies}
          className="btn-white p-3 rounded-lg text-sm"
          disabled={loading}
        >
          Fetch Companies
        </button>
        
        <button 
          onClick={createCompany}
          className="btn-white p-3 rounded-lg text-sm"
          disabled={loading}
        >
          Create Company
        </button>
        
        <button 
          onClick={fetchUsers}
          className="btn-white p-3 rounded-lg text-sm"
          disabled={loading}
        >
          Fetch Users
        </button>
        
        <button 
          onClick={fetchProjects}
          className="btn-white p-3 rounded-lg text-sm"
          disabled={loading}
        >
          Fetch Projects
        </button>
        
        <button 
          onClick={createProject}
          className="btn-white p-3 rounded-lg text-sm"
          disabled={loading}
        >
          Create Project
        </button>
        
        <button 
          onClick={checkIn}
          className="btn-white p-3 rounded-lg text-sm"
          disabled={loading}
        >
          Check In
        </button>
        
        <button 
          onClick={createLeaveRequest}
          className="btn-white p-3 rounded-lg text-sm"
          disabled={loading}
        >
          Create Leave Request
        </button>
      </div>

      {loading && (
        <div className="flex items-center justify-center py-8">
          <LoadingSpinner size={40} />
        </div>
      )}

      {error && (
        <div className="bg-red-500 bg-opacity-10 border border-red-500 text-red-500 px-4 py-3 rounded-lg mb-4">
          {error}
        </div>
      )}

      {data && !loading && (
        <div className="bg-surface-2 p-4 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">API Response:</h3>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(data, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default ApiUsageExamples;
