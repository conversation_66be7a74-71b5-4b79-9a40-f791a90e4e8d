# API Services Documentation

This directory contains all the API services for the TeamCheck frontend application. These services are built using axios and provide a clean interface to interact with the backend API.

## Structure

- `axiosClient.js` - Base axios configuration with interceptors
- `authService.js` - Authentication related API calls
- `companiesService.js` - Company management API calls
- `usersService.js` - User management API calls
- `projectsService.js` - Project management API calls
- `tasksService.js` - Task management API calls
- `attendanceService.js` - Attendance tracking API calls
- `leavesService.js` - Leave management API calls
- `evaluationsService.js` - Performance evaluation API calls
- `index.js` - Exports all services for easy importing

## Usage

### Importing Services

```javascript
import { authService, companiesService, usersService } from '../api';
```

### Authentication

```javascript
// Login
const response = await authService.login({
  email: '<EMAIL>',
  password: 'password123'
});

// Register
const response = await authService.register({
  name: '<PERSON>',
  email: '<EMAIL>',
  password: 'password123'
});

// Get current user
const user = await authService.getCurrentUser();

// Logout
authService.logout();
```

### Companies

```javascript
// Get all companies
const companies = await companiesService.getCompanies();

// Create a company
const newCompany = await companiesService.createCompany({
  name: "Acme Corporation",
  description: "Leading provider of innovative solutions",
  checkInHoursStart: "09:00",
  checkInHoursEnd: "17:00",
  messageFormat: "Daily check-in: What are you working on today?"
});

// Get company by ID
const company = await companiesService.getCompanyById('company-id');

// Update company
const updatedCompany = await companiesService.updateCompany('company-id', {
  name: "Updated Company Name"
});
```

### Projects

```javascript
// Get all projects
const projects = await projectsService.getProjects();

// Create a project
const newProject = await projectsService.createProject({
  title: "New Project",
  description: "Project description",
  companyId: "company-uuid-here",
  dueDate: "2023-12-31T23:59:59Z",
  projectFormat: "statuses"
});

// Add member to project
await projectsService.addProjectMember('project-id', {
  userId: "user-uuid-here",
  role: "developer"
});
```

### Attendance

```javascript
// Check in
const checkInResult = await attendanceService.checkIn({
  companyId: "company-uuid-here",
  checkInMessage: "Starting work on the new feature"
});

// Check out
const checkOutResult = await attendanceService.checkOut({
  companyId: "company-uuid-here",
  checkOutMessage: "Completed the feature implementation",
  projectHours: [
    {
      projectId: "project-uuid-here",
      hours: 4.5
    }
  ]
});

// Get daily overview
const dailyOverview = await attendanceService.getDailyOverview({
  date: '2023-12-15'
});
```

### Error Handling

All API calls should be wrapped in try-catch blocks:

```javascript
try {
  const response = await authService.login(credentials);
  // Handle success
} catch (error) {
  // Handle error
  const errorMessage = error.response?.data?.message || 'An error occurred';
  console.error('Login failed:', errorMessage);
}
```

### Loading States

Use the LoadingSpinner component for loading states:

```javascript
import LoadingSpinner from '../components/common/LoadingSpinner';

const [isLoading, setIsLoading] = useState(false);

const handleSubmit = async () => {
  setIsLoading(true);
  try {
    await authService.login(credentials);
  } catch (error) {
    // Handle error
  } finally {
    setIsLoading(false);
  }
};

// In JSX
{isLoading ? (
  <LoadingSpinner size={20} />
) : (
  "Submit"
)}
```

## Authentication

The axios client automatically handles authentication by:
1. Adding JWT tokens to request headers
2. Redirecting to login on 401 errors
3. Storing/retrieving tokens from localStorage

## Base URL

The base URL is configured in `axiosClient.js` and points to `http://localhost:3000/api` by default.

## Response Format

All API responses are automatically processed by the axios response interceptor and return the `data` property directly.

## Error Handling

The axios client includes error handling for:
- 401 Unauthorized (automatic logout and redirect)
- Network errors
- Server errors

Errors are thrown with the original axios error structure, allowing components to access `error.response.data.message` for user-friendly error messages.
