import { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
import { getCurrentUser } from '../../store/slices/authSlice';
import LoadingSpinner from '../common/LoadingSpinner';
import { getRedirectPath } from '../../utils/authUtils';

/**
 * ProtectedRoute component that checks authentication status
 * Redirects to login if user is not authenticated
 * Shows loading spinner while checking authentication
 */
const ProtectedRoute = ({ children }) => {
  const dispatch = useAppDispatch();
  const location = useLocation();
  const { isAuthenticated, loading, user } = useAppSelector((state) => state.auth);

  useEffect(() => {
    // If we have a token but no user data, try to get current user
    const token = localStorage.getItem('token');
    if (token && !user && !loading) {
      dispatch(getCurrentUser());
    }
  }, [dispatch, user, loading]);

  const redirectPath= getRedirectPath(user)

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <LoadingSpinner size={50} />
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to={redirectPath} state={{ from: location }} replace />;
  }

  if (!user.companyId) {
    return <Navigate to={redirectPath} state={{ from: location }} replace />;
  }

  // Render protected content if authenticated
  return children;
};

export default ProtectedRoute;
