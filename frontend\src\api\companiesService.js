import axiosClient from './axiosClient';

const companiesService = {
  // Get all companies for current user
  getCompanies: async () => {
    try {
      const response = await axiosClient.get('/companies');
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Create new company
  createCompany: async (companyData) => {
    try {
      const response = await axiosClient.post('/companies', companyData);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Get company by ID
  getCompanyById: async (id) => {
    try {
      const response = await axiosClient.get(`/companies/${id}`);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Update company
  updateCompany: async (id, companyData) => {
    try {
      const response = await axiosClient.put(`/companies/${id}`, companyData);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Delete company
  deleteCompany: async (id) => {
    try {
      const response = await axiosClient.delete(`/companies/${id}`);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Get all users in company
  getCompanyUsers: async (id) => {
    try {
      const response = await axiosClient.get(`/companies/${id}/users`);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Invite user to company
  inviteUserToCompany: async (id, inviteData) => {
    try {
      const response = await axiosClient.post(`/companies/${id}/invite`, inviteData);
      return response;
    } catch (error) {
      throw error;
    }
  },
};

export default companiesService;
