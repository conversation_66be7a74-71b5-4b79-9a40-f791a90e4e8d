# TeamCheck Backend

This is the backend for the TeamCheck application, a team management web application designed to help managers monitor their team's activities, projects, performance, and attendance.

## Technology Stack

- **Runtime Environment**: Node.js
- **Framework**: Express.js
- **Database**: MySQL
- **ORM**: Sequelize
- **Authentication**: JWT (JSON Web Tokens)
- **API Format**: RESTful

## Prerequisites

- Node.js (v14 or higher)
- MySQL (v5.7 or higher)

## Setup Instructions

1. **Clone the repository**

```bash
git clone <repository-url>
cd teamcheck/backend
```

2. **Install dependencies**

```bash
npm install
```

3. **Configure environment variables**

Create a `.env` file in the root directory with the following variables:

```
PORT=3000
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_mysql_password
DB_NAME=teamcheck
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=24h
```

4. **Initialize the database**

Run the initialization script which will create the database and tables automatically:

```bash
node init-db.js
```

This script uses Sequelize to create the database and all required tables based on the model definitions.

You can also use the following options:

```bash
# To drop and recreate all tables (WARNING: This will delete all data)
node init-db.js --force

# To alter existing tables to match the models
node init-db.js --alter
```

Note: The server will also automatically sync the database models when it starts, creating tables if they don't exist.

5. **Start the server**

For development:

```bash
npm run dev
```

For production:

```bash
npm start
```

The server will start on the port specified in your `.env` file (default: 3000).

## User Registration Flow

- Users can register without belonging to a company initially
- After registration, users can create a new company or join an existing one
- Users without a company have limited access to the application

## API Endpoints

The API provides the following endpoints:

### Authentication

- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login a user
- `GET /api/auth/me` - Get current user
- `POST /api/auth/verify-email` - Verify user email
- `POST /api/auth/forgot-password` - Request password reset
- `POST /api/auth/reset-password` - Reset password

### Companies

- `GET /api/companies` - Get all companies for current user
  - Requires Authorization header with JWT token

- `POST /api/companies` - Create new company
  ```json
  {
    "name": "Acme Corporation",
    "description": "Leading provider of innovative solutions",
    "logo": "https://example.com/logo.png",
    "checkInHoursStart": "09:00",
    "checkInHoursEnd": "17:00",
    "messageFormat": "Daily check-in: What are you working on today?"
  }
  ```

- `GET /api/companies/:id` - Get company by ID
  - Requires Authorization header with JWT token

- `PUT /api/companies/:id` - Update company
  ```json
  {
    "name": "Acme Corporation Updated",
    "description": "Updated description",
    "logo": "https://example.com/new-logo.png",
    "checkInHoursStart": "08:30",
    "checkInHoursEnd": "17:30",
    "messageFormat": "Updated check-in message format"
  }
  ```

- `DELETE /api/companies/:id` - Delete company
  - Requires Authorization header with JWT token

- `GET /api/companies/:id/users` - Get all users in company
  - Requires Authorization header with JWT token

- `POST /api/companies/:id/invite` - Invite user to company
  ```json
  {
    "email": "<EMAIL>",
    "role": "employee"
  }
  ```

### Users

- `GET /api/users` - Get all users
  - Requires Authorization header with JWT token

- `GET /api/users/:id` - Get user by ID
  - Requires Authorization header with JWT token

- `PUT /api/users/:id` - Update user
  ```json
  {
    "name": "Updated Name",
    "position": "Senior Developer",
    "department": "Engineering",
    "avatar": "https://example.com/avatar.png",
    "status": "active"
  }
  ```

- `DELETE /api/users/:id` - Delete user
  - Requires Authorization header with JWT token

- `POST /api/users/invite` - Invite new user
  ```json
  {
    "email": "<EMAIL>",
    "name": "New User",
    "role": "employee",
    "companyId": "company-uuid-here"
  }
  ```

- `GET /api/users/company/:companyId` - Get all users in a company
  - Requires Authorization header with JWT token

### Projects

- `GET /api/projects` - Get all projects
  - Requires Authorization header with JWT token

- `POST /api/projects` - Create new project
  ```json
  {
    "title": "New Project",
    "description": "Project description goes here",
    "companyId": "company-uuid-here",
    "dueDate": "2023-12-31T23:59:59Z",
    "projectFormat": "statuses"
  }
  ```

- `GET /api/projects/:id` - Get project by ID
  - Requires Authorization header with JWT token

- `PUT /api/projects/:id` - Update project
  ```json
  {
    "title": "Updated Project Title",
    "description": "Updated project description",
    "dueDate": "2024-01-31T23:59:59Z",
    "status": "in progress"
  }
  ```

- `DELETE /api/projects/:id` - Delete project
  - Requires Authorization header with JWT token

- `POST /api/projects/:id/members` - Add members to project
  ```json
  {
    "userId": "user-uuid-here",
    "role": "developer"
  }
  ```

- `DELETE /api/projects/:id/members/:userId` - Remove member from project
  - Requires Authorization header with JWT token

### Tasks

- `GET /api/tasks` - Get all tasks
  - Requires Authorization header with JWT token

- `POST /api/tasks` - Create new task
  ```json
  {
    "title": "New Task",
    "description": "Task description goes here",
    "projectId": "project-uuid-here",
    "companyId": "company-uuid-here",
    "dueDate": "2023-12-15T23:59:59Z",
    "columnId": "column-uuid-here"
  }
  ```

- `GET /api/tasks/:id` - Get task by ID
  - Requires Authorization header with JWT token

- `PUT /api/tasks/:id` - Update task
  ```json
  {
    "title": "Updated Task Title",
    "description": "Updated task description",
    "status": "in progress",
    "dueDate": "2023-12-20T23:59:59Z"
  }
  ```

- `DELETE /api/tasks/:id` - Delete task
  - Requires Authorization header with JWT token

- `PUT /api/tasks/:id/move` - Move task in Kanban
  ```json
  {
    "columnId": "new-column-uuid-here"
  }
  ```

- `POST /api/tasks/:id/comments` - Add comment to task
  ```json
  {
    "text": "This is a comment on the task"
  }
  ```

- `POST /api/tasks/:id/checklist` - Add checklist item
  ```json
  {
    "text": "This is a checklist item"
  }
  ```

- `PUT /api/tasks/:id/checklist/:itemId` - Update checklist item
  ```json
  {
    "completed": true
  }
  ```

### Attendance

- `GET /api/attendance` - Get attendance records
  - Requires Authorization header with JWT token

- `POST /api/attendance/check-in` - Record check-in
  ```json
  {
    "companyId": "company-uuid-here",
    "checkInMessage": "Starting work on the new feature"
  }
  ```

- `POST /api/attendance/check-out` - Record check-out
  ```json
  {
    "companyId": "company-uuid-here",
    "checkOutMessage": "Completed the feature implementation",
    "projectHours": [
      {
        "projectId": "project-uuid-here",
        "hours": 4.5
      },
      {
        "projectId": "another-project-uuid",
        "hours": 3.5
      }
    ]
  }
  ```

- `GET /api/attendance/daily` - Get daily overview
  - Requires Authorization header with JWT token
  - Optional query parameters: `date` (YYYY-MM-DD)

- `GET /api/attendance/logs` - Get detailed logs
  - Requires Authorization header with JWT token
  - Optional query parameters: `startDate`, `endDate`, `userId`

- `GET /api/attendance/hourmap` - Get hours breakdown
  - Requires Authorization header with JWT token
  - Optional query parameters: `startDate`, `endDate`, `userId`

### Leaves

- `GET /api/leaves` - Get leave requests
  - Requires Authorization header with JWT token
  - Optional query parameters: `status`, `startDate`, `endDate`

- `POST /api/leaves` - Create leave request
  ```json
  {
    "companyId": "company-uuid-here",
    "startDate": "2023-12-24",
    "endDate": "2023-12-31",
    "reason": "Holiday vacation"
  }
  ```

- `GET /api/leaves/:id` - Get leave by ID
  - Requires Authorization header with JWT token

- `PUT /api/leaves/:id` - Update leave request
  ```json
  {
    "startDate": "2023-12-25",
    "endDate": "2024-01-02",
    "reason": "Extended holiday vacation"
  }
  ```

- `PUT /api/leaves/:id/approve` - Approve leave
  ```json
  {
    "approvedBy": "manager-user-id-here"
  }
  ```

- `PUT /api/leaves/:id/deny` - Deny leave
  ```json
  {
    "approvedBy": "manager-user-id-here",
    "reason": "Critical project deadline during this period"
  }
  ```

### Evaluations

- `GET /api/evaluations` - Get evaluations
  - Requires Authorization header with JWT token
  - Optional query parameters: `type`, `employeeId`, `evaluatorId`, `status`

- `POST /api/evaluations` - Create evaluation
  ```json
  {
    "companyId": "company-uuid-here",
    "type": "periodic",
    "employeeId": "employee-user-id-here",
    "evaluatorId": "evaluator-user-id-here",
    "projectId": "project-uuid-here",
    "date": "2023-12-15"
  }
  ```

- `GET /api/evaluations/:id` - Get evaluation by ID
  - Requires Authorization header with JWT token

- `PUT /api/evaluations/:id` - Update evaluation
  ```json
  {
    "performanceRating": 4,
    "teamworkRating": 5,
    "communicationRating": 4,
    "initiativeRating": 3,
    "reliabilityRating": 5,
    "feedback": "Great performance overall. Shows excellent teamwork skills."
  }
  ```

- `DELETE /api/evaluations/:id` - Delete evaluation
  - Requires Authorization header with JWT token

- `PUT /api/evaluations/:id/complete` - Complete evaluation
  ```json
  {
    "performanceRating": 4,
    "teamworkRating": 5,
    "communicationRating": 4,
    "initiativeRating": 3,
    "reliabilityRating": 5,
    "feedback": "Great performance overall. Shows excellent teamwork skills.",
    "goals": [
      "Improve technical documentation skills",
      "Take more initiative in project planning",
      "Mentor junior team members"
    ]
  }
  ```

## Project Structure

```
backend/
├── config/             # Sequelize configuration
├── migrations/         # Sequelize migrations
├── models/             # Sequelize models (auto-generated)
├── seeders/            # Sequelize seeders
├── src/
│   ├── config/         # Configuration files
│   ├── controllers/    # Request handlers
│   ├── middleware/     # Express middleware
│   ├── models/         # Database models
│   ├── routes/         # API routes
│   ├── utils/          # Utility functions
│   └── server.js       # Entry point
├── .env                # Environment variables
├── init-db.js          # Database initialization script
├── package.json        # Project dependencies
└── README.md           # Project documentation
```

## License

[MIT](LICENSE)
