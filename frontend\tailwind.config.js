/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      colors: {
        background: "var(--color-background)",
        surface: "var(--color-surface)",
        "surface-2": "var(--color-surface-2)",
        "surface-3": "var(--color-surface-3)",
        primary: "var(--color-primary)",
        secondary: "var(--color-secondary)",
        "text-primary": "var(--color-text-primary)",
        "text-secondary": "var(--color-text-secondary)",
        border: "var(--color-border)",
        "accent-1": "var(--color-accent-1)",
        "accent-2": "var(--color-accent-2)",
        "accent-3": "var(--color-accent-3)",
        "accent-4": "var(--color-accent-4)",
        "soft-blue": "var(--color-soft-blue)",
        "soft-purple": "var(--color-soft-purple)",
        "soft-teal": "var(--color-soft-teal)",
        "soft-pink": "var(--color-soft-pink)",
      },
      fontFamily: {
        sans: ["Inter", "sans-serif"],
      },
      boxShadow: {
        glass: "var(--glass-shadow)",
      },
      backgroundImage: {
        "gradient-dark-1": "var(--gradient-dark-1)",
        "gradient-dark-2": "var(--gradient-dark-2)",
      },
    },
  },
  plugins: [],
};
