import axiosClient from './axiosClient';

const leavesService = {
  // Get leave requests
  getLeaveRequests: async (params = {}) => {
    try {
      const response = await axiosClient.get('/leaves', { params });
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Create leave request
  createLeaveRequest: async (leaveData) => {
    try {
      const response = await axiosClient.post('/leaves', leaveData);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Get leave by ID
  getLeaveById: async (id) => {
    try {
      const response = await axiosClient.get(`/leaves/${id}`);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Update leave request
  updateLeaveRequest: async (id, leaveData) => {
    try {
      const response = await axiosClient.put(`/leaves/${id}`, leaveData);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Approve leave
  approveLeave: async (id, approvalData) => {
    try {
      const response = await axiosClient.put(`/leaves/${id}/approve`, approvalData);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Deny leave
  denyLeave: async (id, denialData) => {
    try {
      const response = await axiosClient.put(`/leaves/${id}/deny`, denialData);
      return response;
    } catch (error) {
      throw error;
    }
  },
};

export default leavesService;
