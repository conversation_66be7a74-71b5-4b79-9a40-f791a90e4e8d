import express from 'express';
import * as analyticsController from '../controllers/analytics.controller.js';
import { authenticate, authorize } from '../middleware/auth.middleware.js';

const router = express.Router();

// Public routes for tracking (no authentication required)
router.post('/track/page-visit', analyticsController.trackPageVisit);
router.post('/track/login-click', analyticsController.trackLoginClick);
router.post('/track/pricing-click', analyticsController.trackPricingClick);

// Protected routes for viewing analytics (admin only)
router.get('/summary', authenticate, authorize(['admin']), analyticsController.getAnalyticsSummary);

export default router;
