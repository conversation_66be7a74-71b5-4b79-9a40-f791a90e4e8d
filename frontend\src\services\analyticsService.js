const API_BASE_URL =
  import.meta.env.VITE_APP_API_URL || "http://localhost:3000";

class AnalyticsService {
  // Track page visit
  static async trackPageVisit(page = "landing", referrer = null) {
    try {
      const response = await fetch(
        `${API_BASE_URL}/analytics/track/page-visit`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            page,
            referrer: referrer || document.referrer || null,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error tracking page visit:", error);
      // Don't throw error to avoid breaking the app
      return { success: false, error: error.message };
    }
  }

  // Track login button click
  static async trackLoginClick(source = "header") {
    try {
      const response = await fetch(
        `${API_BASE_URL}/analytics/track/login-click`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            source,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error tracking login click:", error);
      return { success: false, error: error.message };
    }
  }

  // Track pricing button click
  static async trackPricingClick(
    planName,
    planPrice = null,
    action = "buy_now"
  ) {
    try {
      const response = await fetch(
        `${API_BASE_URL}/analytics/track/pricing-click`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            planName,
            planPrice,
            action,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error tracking pricing click:", error);
      return { success: false, error: error.message };
    }
  }

  // Submit waitlist email
  static async joinWaitlist(email, source = "landing_page") {
    try {
      const response = await fetch(`${API_BASE_URL}/waitlist/join`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email,
          source,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error joining waitlist:", error);
      throw error; // Throw error for waitlist to show user feedback
    }
  }
}

export default AnalyticsService;
