import { useState } from "react";
import { Upload, Download, User, Mail, Briefcase, Users, Calendar } from "lucide-react";
import Modal from "../common/Modal";

const AddEmployeeModal = ({ isOpen, onClose, onAddEmployee }) => {
  const [activeTab, setActiveTab] = useState("single");
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    position: "",
    department: "",
    role: "User",
    dateJoined: new Date().toISOString().split("T")[0]
  });
  const [csvFile, setCsvFile] = useState(null);
  const [csvPreview, setCsvPreview] = useState([]);
  const [dragActive, setDragActive] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (activeTab === "single") {
      onAddEmployee(formData);
    } else {
      // Process CSV data
      onAddEmployee(csvPreview);
    }
    onClose();
  };

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleCsvFile(e.dataTransfer.files[0]);
    }
  };

  const handleCsvUpload = (e) => {
    if (e.target.files && e.target.files[0]) {
      handleCsvFile(e.target.files[0]);
    }
  };

  const handleCsvFile = (file) => {
    setCsvFile(file);
    
    // Read and parse CSV
    const reader = new FileReader();
    reader.onload = (event) => {
      const text = event.target.result;
      const rows = text.split("\n");
      const headers = rows[0].split(",");
      
      const data = [];
      for (let i = 1; i < rows.length; i++) {
        if (rows[i].trim() === "") continue;
        
        const values = rows[i].split(",");
        const entry = {};
        
        headers.forEach((header, index) => {
          entry[header.trim()] = values[index]?.trim() || "";
        });
        
        data.push(entry);
      }
      
      setCsvPreview(data);
    };
    
    reader.readAsText(file);
  };

  const downloadCsvTemplate = () => {
    const headers = "name,email,position,department,role,dateJoined";
    const example = "John Doe,<EMAIL>,Developer,Engineering,User,2023-07-01";
    const csvContent = `${headers}\n${example}`;
    
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", "employee_template.csv");
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose} 
      title="Add Employee" 
      size="lg"
    >
      <div className="mb-6">
        <div className="flex border-b border-border">
          <button
            className={`px-4 py-2 text-sm font-medium border-b-2 ${
              activeTab === "single"
                ? "border-accent-1 text-white"
                : "border-transparent text-text-secondary hover:text-white"
            }`}
            onClick={() => setActiveTab("single")}
          >
            <User className="h-4 w-4 inline mr-2" />
            Add Single Employee
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium border-b-2 ${
              activeTab === "csv"
                ? "border-accent-1 text-white"
                : "border-transparent text-text-secondary hover:text-white"
            }`}
            onClick={() => setActiveTab("csv")}
          >
            <Upload className="h-4 w-4 inline mr-2" />
            Import from CSV
          </button>
        </div>
      </div>

      {activeTab === "single" ? (
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-1">
                Full Name
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <User className="h-4 w-4 text-text-secondary" />
                </div>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full pl-10 p-2.5"
                  placeholder="John Doe"
                  required
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-1">
                Email
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-4 w-4 text-text-secondary" />
                </div>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full pl-10 p-2.5"
                  placeholder="<EMAIL>"
                  required
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-1">
                Position
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Briefcase className="h-4 w-4 text-text-secondary" />
                </div>
                <input
                  type="text"
                  name="position"
                  value={formData.position}
                  onChange={handleInputChange}
                  className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full pl-10 p-2.5"
                  placeholder="Developer"
                  required
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-1">
                Department
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Users className="h-4 w-4 text-text-secondary" />
                </div>
                <input
                  type="text"
                  name="department"
                  value={formData.department}
                  onChange={handleInputChange}
                  className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full pl-10 p-2.5"
                  placeholder="Engineering"
                  required
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-1">
                Role
              </label>
              <select
                name="role"
                value={formData.role}
                onChange={handleInputChange}
                className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
                required
              >
                <option value="Admin">Admin</option>
                <option value="Manager">Manager</option>
                <option value="User">User</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-1">
                Date Joined
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Calendar className="h-4 w-4 text-text-secondary" />
                </div>
                <input
                  type="date"
                  name="dateJoined"
                  value={formData.dateJoined}
                  onChange={handleInputChange}
                  className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full pl-10 p-2.5"
                  required
                />
              </div>
            </div>
          </div>
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 btn-white-ghost font-medium rounded-lg text-sm"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 btn-white font-medium rounded-lg text-sm"
            >
              Add Employee
            </button>
          </div>
        </form>
      ) : (
        <div>
          <div 
            className={`border-2 border-dashed rounded-lg p-6 mb-4 text-center ${
              dragActive ? "border-accent-1 bg-surface-3" : "border-border"
            }`}
            onDragEnter={handleDrag}
            onDragOver={handleDrag}
            onDragLeave={handleDrag}
            onDrop={handleDrop}
          >
            <Upload className="h-10 w-10 mx-auto text-text-secondary mb-3" />
            <p className="text-sm text-text-secondary mb-2">
              Drag and drop your CSV file here, or
            </p>
            <label className="px-4 py-2 btn-white font-medium rounded-lg text-sm cursor-pointer">
              Browse Files
              <input
                type="file"
                accept=".csv"
                className="hidden"
                onChange={handleCsvUpload}
              />
            </label>
            <div className="mt-4">
              <button
                type="button"
                onClick={downloadCsvTemplate}
                className="text-sm text-accent-1 hover:text-accent-2 flex items-center mx-auto"
              >
                <Download className="h-4 w-4 mr-1" />
                Download CSV Template
              </button>
            </div>
          </div>

          {csvFile && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-white mb-2">
                File: {csvFile.name}
              </h4>
              <div className="bg-surface-2 rounded-lg p-3 max-h-60 overflow-auto">
                <table className="w-full text-sm">
                  <thead className="text-xs text-text-secondary uppercase border-b border-border">
                    <tr>
                      {csvPreview.length > 0 &&
                        Object.keys(csvPreview[0]).map((header, index) => (
                          <th key={index} className="px-3 py-2 text-left">
                            {header}
                          </th>
                        ))}
                    </tr>
                  </thead>
                  <tbody>
                    {csvPreview.slice(0, 5).map((row, rowIndex) => (
                      <tr key={rowIndex} className="border-b border-border">
                        {Object.values(row).map((value, valueIndex) => (
                          <td key={valueIndex} className="px-3 py-2 text-white">
                            {value}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
                {csvPreview.length > 5 && (
                  <p className="text-xs text-text-secondary p-2 text-center">
                    Showing 5 of {csvPreview.length} records
                  </p>
                )}
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 btn-white-ghost font-medium rounded-lg text-sm"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleSubmit}
              className="px-4 py-2 btn-white font-medium rounded-lg text-sm"
              disabled={!csvFile}
            >
              Import Employees
            </button>
          </div>
        </div>
      )}
    </Modal>
  );
};

export default AddEmployeeModal;
