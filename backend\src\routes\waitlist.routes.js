import express from 'express';
import * as waitlistController from '../controllers/waitlist.controller.js';
import { authenticate, authorize } from '../middleware/auth.middleware.js';

const router = express.Router();

// Public route for joining waitlist
router.post('/join', waitlistController.joinWaitlist);

// Protected routes for viewing waitlist data (admin only)
router.get('/stats', authenticate, authorize(['admin']), waitlistController.getWaitlistStats);
router.get('/entries', authenticate, authorize(['admin']), waitlistController.getAllWaitlistEntries);

export default router;
