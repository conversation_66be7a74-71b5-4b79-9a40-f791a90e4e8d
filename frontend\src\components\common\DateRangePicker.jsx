import { useState, useEffect } from "react";
import { createPortal } from "react-dom";
import { Calendar, ChevronLeft, ChevronRight, X } from "lucide-react";
import {
  useFloating,
  offset,
  flip,
  shift,
  autoUpdate,
} from "@floating-ui/react-dom";

const DateRangePicker = ({
  onRangeChange,
  initialStartDate = null,
  initialEndDate = null,
  className = "",
  buttonClassName = "",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [startDate, setStartDate] = useState(initialStartDate);
  const [endDate, setEndDate] = useState(initialEndDate);
  const [hoverDate, setHoverDate] = useState(null);
  const [currentMonth, setCurrentMonth] = useState(new Date().getMonth());
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());
  const [selecting, setSelecting] = useState(null); // 'start' or 'end'

  // Setup Floating UI
  const { x, y, strategy, refs } = useFloating({
    placement: "bottom-start",
    middleware: [
      offset(5), // 5px distance from the reference
      flip(), // flip to the opposite side if no space
      shift({ padding: 8 }), // shift along the edge if needed
    ],
    whileElementsMounted: autoUpdate,
  });

  // Format date for display
  const formatDate = (date) => {
    if (!date) return "";
    const d = new Date(date);
    return d.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Handle outside click
  useEffect(() => {
    if (!isOpen) return;

    const handleClickOutside = (event) => {
      const target = event.target;
      // Check if click is outside both the reference and floating elements
      if (
        refs.floating &&
        !refs.floating.contains(target) &&
        refs.reference &&
        !refs.reference.contains(target)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, refs]);

  // Apply date range
  const applyDateRange = () => {
    if (startDate && endDate) {
      onRangeChange(startDate, endDate);
      setIsOpen(false);
    }
  };

  // Clear date range
  const clearDateRange = () => {
    setStartDate(null);
    setEndDate(null);
    setSelecting("start");
    onRangeChange(null, null);
  };

  // Navigate to previous month
  const prevMonth = () => {
    if (currentMonth === 0) {
      setCurrentMonth(11);
      setCurrentYear(currentYear - 1);
    } else {
      setCurrentMonth(currentMonth - 1);
    }
  };

  // Navigate to next month
  const nextMonth = () => {
    if (currentMonth === 11) {
      setCurrentMonth(0);
      setCurrentYear(currentYear + 1);
    } else {
      setCurrentMonth(currentMonth + 1);
    }
  };

  // Get days in month
  const getDaysInMonth = (year, month) => {
    return new Date(year, month + 1, 0).getDate();
  };

  // Get day of week for first day of month
  const getFirstDayOfMonth = (year, month) => {
    return new Date(year, month, 1).getDay();
  };

  // Check if date is in range
  const isInRange = (year, month, day) => {
    if (!startDate || !endDate) return false;

    const date = new Date(year, month, day);
    const start = new Date(startDate);
    const end = new Date(endDate);

    return date >= start && date <= end;
  };

  // Check if date is the start date
  const isStartDate = (year, month, day) => {
    if (!startDate) return false;

    const date = new Date(year, month, day);
    const start = new Date(startDate);

    return date.getTime() === start.getTime();
  };

  // Check if date is the end date
  const isEndDate = (year, month, day) => {
    if (!endDate) return false;

    const date = new Date(year, month, day);
    const end = new Date(endDate);

    return date.getTime() === end.getTime();
  };

  // Check if date is in hover range
  const isInHoverRange = (year, month, day) => {
    if (!startDate || !hoverDate || endDate) return false;

    const date = new Date(year, month, day);
    const start = new Date(startDate);
    const hover = new Date(hoverDate);

    return (date > start && date <= hover) || (date < start && date >= hover);
  };

  // Handle date click
  const handleDateClick = (year, month, day) => {
    const clickedDate = new Date(year, month, day);

    if (!startDate || selecting === "start") {
      setStartDate(clickedDate);
      setEndDate(null);
      setSelecting("end");
    } else {
      if (clickedDate < new Date(startDate)) {
        setEndDate(new Date(startDate));
        setStartDate(clickedDate);
      } else {
        setEndDate(clickedDate);
      }
      setSelecting("start");
    }
  };

  // Handle date hover
  const handleDateHover = (year, month, day) => {
    setHoverDate(new Date(year, month, day));
  };

  // Render calendar
  const renderCalendar = () => {
    const daysInMonth = getDaysInMonth(currentYear, currentMonth);
    const firstDayOfMonth = getFirstDayOfMonth(currentYear, currentMonth);
    const monthName = new Date(currentYear, currentMonth).toLocaleString(
      "default",
      { month: "long" }
    );

    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push(<div key={`empty-${i}`} className="w-8 h-8"></div>);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const isStart = isStartDate(currentYear, currentMonth, day);
      const isEnd = isEndDate(currentYear, currentMonth, day);
      const inRange = isInRange(currentYear, currentMonth, day);
      const inHoverRange = isInHoverRange(currentYear, currentMonth, day);

      days.push(
        <button
          key={day}
          type="button"
          className={`h-8 w-8 rounded-full flex items-center justify-center text-sm
            ${isStart || isEnd ? "bg-accent-1 text-white" : ""}
            ${inRange || inHoverRange ? "bg-accent-1/20" : ""}
            ${
              !isStart && !isEnd && !inRange && !inHoverRange
                ? "hover:bg-surface-3"
                : ""
            }
          `}
          onClick={() => handleDateClick(currentYear, currentMonth, day)}
          onMouseEnter={() => handleDateHover(currentYear, currentMonth, day)}
        >
          {day}
        </button>
      );
    }

    return (
      <div className="p-4">
        <div className="flex justify-between items-center mb-4">
          <button
            type="button"
            className="p-1 rounded-full hover:bg-surface-3"
            onClick={prevMonth}
          >
            <ChevronLeft className="w-5 h-5" />
          </button>
          <div className="text-sm font-medium">
            {monthName} {currentYear}
          </div>
          <button
            type="button"
            className="p-1 rounded-full hover:bg-surface-3"
            onClick={nextMonth}
          >
            <ChevronRight className="w-5 h-5" />
          </button>
        </div>
        <div className="grid grid-cols-7 gap-1 mb-2">
          {["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"].map((day) => (
            <div
              key={day}
              className="flex justify-center items-center w-8 h-8 text-xs text-text-secondary"
            >
              {day}
            </div>
          ))}
        </div>
        <div className="grid grid-cols-7 gap-1">{days}</div>
      </div>
    );
  };

  return (
    <div className={`relative ${className}`}>
      <button
        type="button"
        className={`flex items-center px-3 py-1 text-sm rounded-lg btn-white-ghost ${buttonClassName}`}
        onClick={() => setIsOpen(!isOpen)}
        ref={refs.setReference}
      >
        <Calendar className="mr-2 w-4 h-4" />
        {startDate && endDate ? (
          <span>
            {formatDate(startDate)} - {formatDate(endDate)}
          </span>
        ) : (
          <span>Select Date Range</span>
        )}
      </button>

      {isOpen &&
        createPortal(
          <div
            ref={refs.setFloating}
            style={{
              position: strategy,
              top: y ?? 0,
              left: x ?? 0,
              width: "max-content",
              zIndex: 9999, // Higher z-index to ensure it's above everything
            }}
            className="p-2 rounded-lg border shadow-lg bg-surface-2 border-border scrollable-area"
          >
            <div className="flex">{renderCalendar()}</div>
            <div className="flex justify-between items-center px-4 pt-3 border-t border-border">
              <button
                type="button"
                className="flex items-center text-sm text-accent-4 hover:text-accent-3"
                onClick={clearDateRange}
              >
                <X className="mr-1 w-3 h-3" />
                Clear
              </button>
              <div>
                <button
                  type="button"
                  className="px-3 py-1 mr-2 text-sm rounded-lg btn-white-ghost"
                  onClick={() => setIsOpen(false)}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="px-3 py-1 text-sm rounded-lg btn-white"
                  onClick={applyDateRange}
                  disabled={!startDate || !endDate}
                >
                  Apply
                </button>
              </div>
            </div>

            {/* Selection status */}
            {(startDate || endDate) && (
              <div className="px-4 py-2 mt-2 text-xs rounded-lg bg-surface-3 text-text-secondary">
                {startDate && !endDate && (
                  <p>
                    Start date selected: {formatDate(startDate)}. Please select
                    an end date.
                  </p>
                )}
                {startDate && endDate && (
                  <p>
                    Selected range: {formatDate(startDate)} -{" "}
                    {formatDate(endDate)}
                  </p>
                )}
              </div>
            )}
          </div>,
          document.body
        )}
    </div>
  );
};

export default DateRangePicker;
