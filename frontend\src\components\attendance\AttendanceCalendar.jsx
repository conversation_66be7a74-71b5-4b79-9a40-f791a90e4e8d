import { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';
import { useUserDetails } from '../../context/UserDetailsContext';

const AttendanceCalendar = () => {
  // State for current month and year
  const [currentDate, setCurrentDate] = useState(new Date());
  // State for mock check-ins data
  const [mockCheckIns, setMockCheckIns] = useState({});
  // Get the user details context
  const { openUserModal } = useUserDetails();

  // Get days in month
  const getDaysInMonth = (year, month) => {
    return new Date(year, month + 1, 0).getDate();
  };

  // Format date as YYYY-MM-DD for lookup in mockCheckIns
  const formatDate = (year, month, day) => {
    return `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
  };

  // Generate mock data for user check-ins based on current month
  // In a real app, this would come from an API
  const generateMockCheckIns = (date = currentDate) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const daysInMonth = getDaysInMonth(year, month);

    const mockData = {};
    const users = ['JD', 'SW', 'MJ', 'JS', 'AK', 'RL', 'TW', 'BK', 'CP', 'DM'];

    // Generate random check-ins for weekdays (skip weekends)
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);
      const dayOfWeek = date.getDay();

      // Skip weekends (0 = Sunday, 6 = Saturday)
      if (dayOfWeek === 0 || dayOfWeek === 6) continue;

      // Format the date key
      const dateKey = formatDate(year, month, day);

      // Randomly select 3-8 users who checked in
      // For some days, make it more than 4 users to test the +X feature
      const numUsers = Math.floor(Math.random() * 6) + 3; // 3-8 users
      const checkedInUsers = [];

      // Ensure JD (John Doe) is always present
      checkedInUsers.push('JD');

      // Add other random users
      while (checkedInUsers.length < numUsers) {
        const randomUser = users[Math.floor(Math.random() * users.length)];
        if (!checkedInUsers.includes(randomUser)) {
          checkedInUsers.push(randomUser);
        }
      }

      // For specific days (e.g., 5th, 12th, 19th), ensure we have 8 users to test the +X feature
      if (day % 7 === 5) {
        checkedInUsers.length = 0; // Clear the array
        checkedInUsers.push('JD', 'SW', 'MJ', 'JS', 'AK', 'RL', 'TW', 'BK');
      }

      mockData[dateKey] = checkedInUsers;
    }

    return mockData;
  };

  // Effect to regenerate mock data when the month changes
  useEffect(() => {
    setMockCheckIns(generateMockCheckIns(currentDate));
  }, [currentDate]);

  // Get day of week for the first day of the month (0 = Sunday, 1 = Monday, etc.)
  const getFirstDayOfMonth = (year, month) => {
    return new Date(year, month, 1).getDay();
  };

  // Navigate to previous month
  const prevMonth = () => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      newDate.setMonth(newDate.getMonth() - 1);
      return newDate;
    });
  };

  // Navigate to next month
  const nextMonth = () => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      newDate.setMonth(newDate.getMonth() + 1);
      return newDate;
    });
  };

  // Navigate to previous year
  const prevYear = () => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      newDate.setFullYear(newDate.getFullYear() - 1);
      return newDate;
    });
  };

  // Navigate to next year
  const nextYear = () => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      newDate.setFullYear(newDate.getFullYear() + 1);
      return newDate;
    });
  };

  // Get check-ins for a specific day
  const getCheckInsForDay = (year, month, day) => {
    const dateKey = formatDate(year, month, day);
    return mockCheckIns[dateKey] || [];
  };

  // Generate calendar grid
  const generateCalendarGrid = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const daysInMonth = getDaysInMonth(year, month);
    const firstDayOfMonth = getFirstDayOfMonth(year, month);

    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push(<div key={`empty-${i}`} className="h-20 md:h-24 border border-border bg-surface-2 bg-opacity-10 rounded-md"></div>);
    }

    // Add cells for each day of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const checkIns = getCheckInsForDay(year, month, day);
      const isToday = new Date().getDate() === day &&
                      new Date().getMonth() === month &&
                      new Date().getFullYear() === year;

      // Check if it's a weekend
      const date = new Date(year, month, day);
      const isWeekend = date.getDay() === 0 || date.getDay() === 6;

      days.push(
        <div
          key={`day-${day}`}
          className={`h-20 md:h-24 border border-border ${
            isToday
              ? 'shadow-md shadow-surface-3 bg-surface ring-1'
              : isWeekend
                ? 'bg-surface-3 bg-opacity-5'
                : 'bg-surface-2 bg-opacity-10'
          } rounded-md p-2 relative transition-all hover:border-text-secondary`}
        >
          <div className={`text-sm font-medium ${isToday ? 'text-soft-blue' : 'text-white'} flex justify-between items-center`}>
            <span>{day}</span>
            {checkIns.length > 0 && (
              <span className="text-xs text-text-secondary">{checkIns.length}</span>
            )}
          </div>

          <div className="flex flex-wrap gap-1.5 mt-2">
            {/* Show first 4 users */}
            {checkIns.slice(0, 4).map((user, index) => (
              <div
                key={`${day}-${user}-${index}`}
                className={`h-6 w-6 rounded-full ${getUserColor(user).split(' ')[0]} flex items-center justify-center cursor-pointer hover:opacity-80 transition-opacity`}
                title={
                  user === 'JD' ? 'John Doe' :
                  user === 'SW' ? 'Sarah Williams' :
                  user === 'MJ' ? 'Mike Johnson' :
                  user === 'JS' ? 'Jane Smith' :
                  user === 'AK' ? 'Alex Kim' :
                  user === 'RL' ? 'Rachel Lee' :
                  user === 'TW' ? 'Tom Wilson' :
                  user === 'BK' ? 'Brian Kim' :
                  user === 'CP' ? 'Chris Park' :
                  user === 'DM' ? 'David Miller' : user
                }
                onClick={() => openUserModal(user)}
              >
                <span className={`text-xs font-medium ${getUserColor(user).split(' ')[1]}`}>{user}</span>
              </div>
            ))}

            {/* Show +X indicator if there are more than 4 users */}
            {checkIns.length > 4 && (
              <div
                className="h-6 w-6 rounded-full bg-accent-4/70 flex items-center justify-center group relative cursor-pointer hover:bg-accent-4/100 transition-colors"
                title="More users"
              >
                <span className="text-xs font-medium text-white">+{checkIns.length - 4}</span>

                {/* Smart tooltip that positions itself based on available space */}
                <div
                  className="tooltip-container absolute left-1/2 transform -translate-x-1/2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-opacity z-10"
                  style={{
                    // Check if we're in the first or second row (days 1-14)
                    // If so, position below, otherwise position above
                    top: day <= 14 ? 'calc(100% - 10px)' : 'auto',
                    bottom: day > 14 ? 'calc(100% - 10px)' : 'auto',
                    // Create a large invisible bridge between the icon and tooltip
                    width: '240px',
                    // Add extra padding to create a larger hover area
                    padding: '30px'
                  }}
                >
                  {/* Actual tooltip content */}
                  <div className="w-max max-w-[200px] bg-surface-2 border border-border text-white text-xs rounded-md p-3 shadow-lg relative">
                    <div className="text-center font-medium mb-2 pb-1 border-b border-border">
                      Additional Team Members
                    </div>
                    <div className="flex flex-col gap-2">
                      {checkIns.slice(4).map((user, index) => (
                        <div
                          key={`tooltip-${day}-${user}-${index}`}
                          className="flex items-center cursor-pointer hover:bg-surface-3 p-1.5 rounded transition-colors"
                          onClick={(e) => {
                            e.stopPropagation(); // Prevent closing the tooltip
                            openUserModal(user);
                          }}
                        >
                          <div className={`h-4 w-4 rounded-full ${getUserColor(user).split(' ')[0]} flex items-center justify-center mr-2`}>
                            <span className={`text-[10px] font-medium ${getUserColor(user).split(' ')[1]}`}>{user}</span>
                          </div>
                          <span>
                            {user === 'JD' ? 'John Doe' :
                            user === 'SW' ? 'Sarah Williams' :
                            user === 'MJ' ? 'Mike Johnson' :
                            user === 'JS' ? 'Jane Smith' :
                            user === 'AK' ? 'Alex Kim' :
                            user === 'RL' ? 'Rachel Lee' :
                            user === 'TW' ? 'Tom Wilson' :
                            user === 'BK' ? 'Brian Kim' :
                            user === 'CP' ? 'Chris Park' :
                            user === 'DM' ? 'David Miller' : user}
                          </span>
                        </div>
                      ))}
                    </div>

                    {/* Invisible bridge to ensure continuous hover area */}
                    <div
                      style={{
                        position: 'absolute',
                        left: '50%',
                        transform: 'translateX(-50%)',
                        width: '50px',
                        height: day <= 14 ? '30px' : '30px',
                        bottom: day <= 14 ? '100%' : 'auto',
                        top: day > 14 ? '100%' : 'auto'
                      }}
                    ></div>

                    {/* Conditional Arrow - points up or down based on tooltip position */}
                    {day <= 14 ? (
                      // Arrow pointing up (for tooltips positioned below)
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[8px] border-r-[8px] border-b-[8px] border-l-transparent border-r-transparent border-b-surface-2"></div>
                    ) : (
                      // Arrow pointing down (for tooltips positioned above)
                      <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[8px] border-r-[8px] border-t-[8px] border-l-transparent border-r-transparent border-t-surface-2"></div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      );
    }

    return days;
  };

  // Month names for display
  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  // Day names for display
  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  // Get total unique users who checked in this month (for future use)
  const getUniqueUsersThisMonth = () => {
    const uniqueUsers = new Set();

    Object.values(mockCheckIns).forEach(users => {
      users.forEach(user => uniqueUsers.add(user));
    });

    return Array.from(uniqueUsers);
  };

  // Get user color based on initials
  const getUserColor = (initials) => {
    const colors = {
      'JD': 'bg-soft-pink/20 text-soft-pink',
      'SW': 'bg-accent-2/20 text-accent-2',
      'MJ': 'bg-soft-purple/20 text-soft-purple',
      'JS': 'bg-accent-4/20 text-accent-4',
      'AK': 'bg-accent-1/20 text-accent-1',
      'RL': 'bg-accent-3/20 text-accent-3',
      'TW': 'bg-soft-teal/20 text-soft-teal',
      'BK': 'bg-soft-pink/20 text-soft-pink',
      'CP': 'bg-accent-2/20 text-accent-2',
      'DM': 'bg-soft-purple/20 text-soft-purple'
    };

    return colors[initials] || 'bg-surface-3/20 text-white';
  };

  return (
    <div className="glass-card rounded-lg overflow-hidden">
      {/* Calendar navigation */}
      <div className="flex justify-between items-center p-4 border-b border-border">
        <div className="flex items-center space-x-1">
          <button
            onClick={prevYear}
            className="w-7 h-7 flex items-center justify-center rounded-full bg-surface-3 text-text-secondary hover:text-white hover:bg-surface transition-colors"
            title="Previous Year"
          >
            <ChevronsLeft className="h-4 w-4" />
          </button>
          <button
            onClick={prevMonth}
            className="w-7 h-7 flex items-center justify-center rounded-full bg-surface-3 text-text-secondary hover:text-white hover:bg-surface transition-colors"
            title="Previous Month"
          >
            <ChevronLeft className="h-4 w-4" />
          </button>
        </div>

        <div className="text-white font-medium text-lg">
          {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
        </div>

        <div className="flex items-center space-x-1">
          <button
            onClick={nextMonth}
            className="w-7 h-7 flex items-center justify-center rounded-full bg-surface-3 text-text-secondary hover:text-white hover:bg-surface transition-colors"
            title="Next Month"
          >
            <ChevronRight className="h-4 w-4" />
          </button>
          <button
            onClick={nextYear}
            className="w-7 h-7 flex items-center justify-center rounded-full bg-surface-3 text-text-secondary hover:text-white hover:bg-surface transition-colors"
            title="Next Year"
          >
            <ChevronsRight className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Calendar grid */}
      <div className="p-4 overflow-x-auto">
        <div className="min-w-[768px]">
          {/* Day headers */}
          <div className="grid grid-cols-7 gap-2 mb-2">
            {dayNames.map(day => (
              <div key={day} className="text-center text-text-secondary text-sm font-medium">
                {day}
              </div>
            ))}
          </div>

          {/* Calendar days */}
          <div className="grid grid-cols-7 gap-2">
            {generateCalendarGrid()}
          </div>
        </div>
      </div>

      <div className="px-4 py-3 bg-surface-2 border-t border-border text-xs text-text-secondary">
        <p>Each circle represents a team member who checked in on that day. Days with more than 4 check-ins show a "+X" indicator - hover over it to see additional team members. Click on any user circle to view detailed information.</p>
      </div>
    </div>
  );
};

export default AttendanceCalendar;
